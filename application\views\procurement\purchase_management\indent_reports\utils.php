<style>
    .daterangepicker .applyBtn {
        background-color: #343a40 !important;
        border-color: #343a40 !important;
        color: #fff !important;
    }

    .custom-card {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 5px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }
</style>

<!-- datatable styles -->
<style>
    .breadcrumb {
        background-color: #f8f9fa;
        padding: 0.75rem 1rem;
        border-radius: 5px;
        margin-bottom: 1rem;
    }

    /* .breadcrumb li a {
        color: #007bff;
        text-decoration: none;
    } */

    /* .breadcrumb li a:hover {
        text-decoration: underline;
    } */

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }

    .btn-primary {
        background-color: #007bff;
        border-color: #007bff;
    }

    .btn-primary:hover {
        background-color: #0056b3;
        border-color: #004085;
    }

    .purchase-container {
        background-color: #ffffff;
        /* border: 1px solid #dee2e6; */
        border-radius: 5px;
        padding: 1rem;
    }

    .purchase-details-table {
        margin-top: 1rem;
    }

    .purchase-details-table table {
        border-collapse: collapse;
        width: 100%;
    }

    .purchase-details-table th,
    .purchase-details-table td {
        border: 1px solid #dee2e6;
        padding: 0.75rem;
        text-align: left;
    }

    .purchase-details-table th {
        background-color: #f8f9fa;
    }

    .dataTables_wrapper .dt-buttons {
        float: right;
    }

    .dataTables_filter input {
        background-color: #f2f2f2;
        border: 1px solid #ccc;
        border-radius: 4px;
        margin-right: 5px;
    }

    .dataTables_wrapper .dataTables_filter {
        float: right;
        text-align: left;
        width: unset;
    }

    .dt-buttons {
        margin-bottom: 5px;
    }

    /* scrollbar area */
    /* Hide the default scrollbar */
    ::-webkit-scrollbar {
        width: 5px;
        height: 9px;
    }

    /* Create a custom scrollbar */
    ::-webkit-scrollbar-track {
        background-color: #f2f2f2;
        border-radius: 10px;
    }

    /* Create a thumb for the scrollbar */
    ::-webkit-scrollbar-thumb {
        /* background-color: #007bff;  */
        border-radius: 100px;
    }

    /* Make the scrollbar visible when hovering over the track */
    ::-webkit-scrollbar-track-piece-over:hover {
        background-color: #ddd;
    }

    /* Make the scrollbar thumb visible when hovering over it */
    ::-webkit-scrollbar-thumb:hover {
        background-color: #C7C8CC;
    }

    .swal-wide {
        max-width: 600px !important;
        /* Set a proper max width for the modal */
        width: 90% !important;
        /* Ensure responsiveness */
        padding: 20px !important;
        /* Add padding for better spacing */
    }

    .daterangepicker .applyBtn {
        background-color: #343a40 !important;
        border-color: #343a40 !important;
        color: #fff !important;
    }
</style>

<link rel="stylesheet"
    href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.14.0-beta2/css/bootstrap-select.min.css" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.14.0-beta2/js/bootstrap-select.min.js"></script>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript"
    src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script type="text/javascript">
    // Format currency (e.g., ₹1,234.56)
    function formatCurrency(amount, currency = "INR") {
        return Intl.NumberFormat("en-IN", {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 2
        }).format(amount);
    }

    // Format number with Indian digit system (e.g., 12,345)
    function formatNumber(number) {
        return new Intl.NumberFormat('en-IN').format(number);
    }

    // Generate alert-style message box
    function generateMessageHelper(msg = "Loading...") {
        return `
        <div style="
            color: black;
            background: #ebf3ff;
            border: 2px solid #fffafa;
            border-radius: 6px;
            padding: 10px;
            font-size: 14px;
            margin: 14px 0;
            text-align: center;">
            ${msg}
        </div>`;
    }

    function initializeDateRangePicker() {
        $('#reportrange').on('show.daterangepicker', function (ev, picker) {
            picker.container.find('.applyBtn').removeClass('btn-success').addClass('btn-dark');
        });

        const end = moment(); // Today
        const start = moment().subtract(6, 'months'); // 6 months ago

        $("#reportrange").daterangepicker({
            maxDate: end,
            ranges: {
                'Today': [end.clone(), end.clone()],
                'Yesterday': [end.clone().subtract(1, 'days'), end.clone().subtract(1, 'days')],
                'Last 7 Days': [end.clone().subtract(6, 'days'), end.clone()],
                'Last 30 Days': [end.clone().subtract(29, 'days'), end.clone()],
                'Last 6 Months': [start.clone(), end.clone()],
                // 'All Time': [moment("2025-01-01"), end.clone()]
            },
            opens: 'right',
            format: 'DD-MM-YYYY',
            startDate: start,
            endDate: end
        }, function (start, end) {
            $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
            $('#from_date').val(start.format('DD-MM-YYYY'));
            $('#to_date').val(end.format('DD-MM-YYYY'));
        });

        // Set initial visible values
        $("#reportrange span").html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
        $('#from_date').val(start.format('DD-MM-YYYY'));
        $('#to_date').val(end.format('DD-MM-YYYY'));
    }

    initializeDateRangePicker();

    function initDataTable(tableId) {
        $(`#${tableId}`).DataTable({
            "language": {
                "search": "",
                "searchPlaceholder": "Enter Search..."
            },
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "pageLength": 10,
            "order": false,
            dom: 'lBfrtip',
            buttons: [
                {
                    extend: 'excelHtml5',
                    text: 'Excel',
                    filename: "purchase order",
                    className: 'btn btn-dark'
                },
                {
                    extend: 'print',
                    text: 'Print',
                    autoPrint: true,
                    filename: "purchase order",
                    className: 'btn btn-dark'
                },
                {
                    extend: 'pdfHtml5',
                    text: 'PDF',
                    filename: "purchase order",
                    className: 'btn btn-dark'
                }
            ]
        });
    }
</script>