<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2'); ?>">Procurement</a></li>
    <li><a href="<?php echo site_url('procurement/inventory_controller_v2/indent_widgets_v2'); ?>">Indent Management</a>
    </li>
    <li>Manage Indents</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="panel-header"
                style="margin: 0px; background: none; border-bottom: 1px solid lightgray; height: 3.7rem;">
                <h3>
                    <a style="" class="back_anchor"
                        href="<?php echo site_url('procurement/inventory_controller_v2/indent_widgets_v2') ?>"
                        class="control-primary">
                        <span class="fa fa-arrow-left"></span>
                    </a>
                    Manage Indents
                    <?php if ($this->authorization->isAuthorized('INDENT.ADMIN') == 1) { ?>
                        <a id="createBomBtntn" data-target="#BOM_modal" data-toggle="modal">
                            <div class="pull-right"
                                style="background: #676363;padding: 5px;border-radius: 9px;color: #fff;font-size: 1rem;padding-right: 1.4rem;cursor: pointer;height: 3rem;display: flex;justify-content: center;align-items: center;">
                                <div style="display: flex;justify-content: center;align-items: center;">
                                    <svg style="height: 1.3rem;width: 3rem;" xmlns="http://www.w3.org/2000/svg" width="16"
                                        height="16" fill="currentColor" class="bi bi-plus-lg" viewBox="0 0 16 16">
                                        <path fill-rule="evenodd"
                                            d="M8 2a.5.5 0 0 1 .5.5v5h5a.5.5 0 0 1 0 1h-5v5a.5.5 0 0 1-1 0v-5h-5a.5.5 0 0 1 0-1h5v-5A.5.5 0 0 1 8 2">
                                        </path>
                                    </svg>Create New
                                </div>
                            </div>
                        </a>
                    <?php } ?>
                </h3>
            </div>
        </div>
        <div class="col-md-12">
            <div class="purchase-container">
                <div class="row mb-5">
                    <div class="col-md-2 form-group">
                        <label class="control-label">Date Range</label>
                        <div id="reportrange" class="dtrange" style="width: 100%">
                            <span></span>
                            <input type="hidden" id="from_date">
                            <input type="hidden" id="to_date">
                        </div>
                    </div>

                    <div class="col-md-2 form-group">
                        <label class="control-label">Indent Status</label>
                        <select title="All" class="form-control selectpicker" name="indentStatus[]" id="indentStatus"
                            multiple>
                            <option value="0">Pending</option>
                            <option value="2">Rejected</option>
                            <option value="4">Pre-Indent Approval Pending</option>
                            <option value="3">Pre-Indent Modification Pending</option>
                            <option value="6">Pre-Indent Approved</option>
                            <option value="5">Quotation Approval Pending</option>
                            <option value="7">Quotation Modification Pending</option>
                            <option value="1">Indent Approved</option>
                        </select>
                    </div>

                    <div class="col-md-2 form-group">
                        <label class="control-label">Indent Type</label>
                        <select title="All" class="form-control selectpicker" name="poType" id="poType">
                            <option value="All">All</option>
                            <option value="associatedToMe">Requires Approval by me</option>
                            <option value="createdByMe">Created by me</option>
                        </select>
                    </div>

                    <div class="col-md-2 d-flex align-items-end" style="height: 4.4rem;">
                        <button onclick="getIndents()" class="btn btn-primary" style="width: 120px;">
                            <i class="fa fa-file-alt"></i> Get Indents
                        </button>
                    </div>
                </div>
                <div class="indent-details-table">
                    <!-- purchase details table goes here -->
                    <div style="color:red;text-align:center;
                        color: black;
                        border: 2px solid #fffafa;
                        text-align: center;
                        border-radius: 6px;
                        position: relative;
                        margin-left: 14px;
                        padding: 10px;
                        font-size: 14px;
                        margin-top: 14px;
                        background: #ebf3ff;
                        margin-bottom: 1rem;">
                        Loading...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $this->load->view("procurement/purchase_management/bill_of_materials/bom_modal.php") ?>

<link rel="stylesheet"
    href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.14.0-beta2/css/bootstrap-select.min.css" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.14.0-beta2/js/bootstrap-select.min.js"></script>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript"
    src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script type="text/javascript">
    $(document).ready(function () {
        // fns to run onload
        getIndents();
    });

    function formatCurrency(amount, currency = "INR") {
        return Intl.NumberFormat("en-In", {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 2
        }).format(amount)
    }

    function formatNumber(number) {
        return new Intl.NumberFormat('en-IN', { maximumSignificantDigits: 3 }).format(
            number,
        )
    }

    $('#reportrange').on('show.daterangepicker', function (ev, picker) {
        picker.container.find('.applyBtn').removeClass('btn-success').addClass('btn-dark');
    });

    const end = moment(); // Today
    const start = moment().subtract(6, 'months'); // 6 months ago

    $("#reportrange").daterangepicker({
        maxDate: end,
        ranges: {
            'Today': [end.clone(), end.clone()],
            'Yesterday': [end.clone().subtract(1, 'days'), end.clone().subtract(1, 'days')],
            'Last 7 Days': [end.clone().subtract(6, 'days'), end.clone()],
            'Last 30 Days': [end.clone().subtract(29, 'days'), end.clone()],
            'Last 6 Months': [start.clone(), end.clone()],
            // 'All Time': [moment("2025-01-01"), end.clone()]
        },
        opens: 'right',
        format: 'DD-MM-YYYY',
        startDate: start,
        endDate: end
    }, function (start, end) {
        $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
        $('#from_date').val(start.format('DD-MM-YYYY'));
        $('#to_date').val(end.format('DD-MM-YYYY'));
    });

    // Set initial visible values
    $("#reportrange span").html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date').val(start.format('DD-MM-YYYY'));
    $('#to_date').val(end.format('DD-MM-YYYY'));

    function generateMessageHelper(msg = "Loading...") {
        msg = `
  <div style="color:red;text-align:center;
    color: black;
    border: 2px solid #fffafa;
    text-align: center;
    border-radius: 6px;
    position: relative;
    margin-left: 14px;
    padding: 10px;
    font-size: 14px;
    margin-top: 14px;
    background: #ebf3ff;
    margin-bottom: 1rem;">
      ${msg}
    </div>
  `;

        return msg;
    }

    const indentStatusDescription = {
        0: "Pending",
        1: "Indent Approved",
        2: "Rejected",
        3: "Pre-Indent Modification Pending",   // sent request for modification
        4: "Pre-Indent Approval Pending",   // sent for indent approval
        5: "Quotation Approval Pending",   // sent for quotation approval
        6: "Pre-Indent Approved",     // Indent Approved
        7: "Quotation Modification Pending"     // Quotation Approved
    };

    function getIndents() {
        const fromDate = $("#from_date").val();
        const toDate = $("#to_date").val();
        const indentStatus = $("#indentStatus").val();
        const poType = $("#poType").val();

        $(".indent-details-table").html(generateMessageHelper("Loading..."));

        $.ajax({
            url: "<?php echo site_url('procurement/Requisition_controller_v2/get_indents') ?>",
            type: "POST",
            data: { fromDate, toDate, indentStatus, poType },
            success: function (data) {
                const response = JSON.parse(data);
                if (response.length) {
                    // create table and insert
                    let html = `<table class="table table-bordered" id="indent-details" style="white-space: nowrap;">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Indent Name</th>
                                        <th>Department</th>
                                        <th>Status</th>
                                        <th>Created By</th>
                                        <th>Created On</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>`;

                    response.forEach((indent, idx) => {
                        html += ` <tr>
                                        <td>${idx + 1}</td>
                                        <td>${indent.indent_name || "-"}</td>
                                        <td>${indent.department_name || "-"}</td>
                                        <td>${indentStatusDescription[indent.status] || "-"}</td>
                                        <td>${indent.created_by_name || "SUPER ADMIN"}</td>
                                        <td>${indent.created_on || "-"}</td>
                                        <td>
                                            <button class="btn btn-primary" onclick="viewIndent('${indent.id}')">Manage Indent</button>
                                        </td>
                                    </tr>`;
                    });

                    $(".indent-details-table").html(html);
                } else {
                    $(".indent-details-table").html(generateMessageHelper("No Indents Found"));
                }
            }
        })
    }

    function viewIndent(indentId) {
        const url = "<?php echo site_url('procurement/Requisition_controller_v2/view_indent') ?>/" + indentId;
        window.open(url, '_blank');
    }
</script>