<?php
/**
 * Name:    OxygenV2
 * Author:  Anish
 *          <EMAIL>
 *
 * Created:  26 april 2023
 *
 * Description: Controller for Inentory Module. Entry point for Inentory Module
 *
 * Requirements: PHP5 or above
 *
 */

class Inventory_controller_v2 extends CI_Controller {

    function __construct() {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('PROCUREMENT_INVENTORY') || !$this->authorization->isAuthorized('PROCUREMENT_INVENTORY.MODULE')) {
        redirect('dashboard', 'refresh');
        }

        $this->load->library('filemanager');
        $this->load->model('procurement/Inventory_model_v2');
        $this->load->model('procurement/Sales_model_v2');
        $this->config->load('form_elements');
        $this->load->library('fee_library');
       
    }
    
    public function add_category() {
        $category_name = $_POST['category_name'];
        $is_sellable = $_POST['is_sellable'];
        $category_description= $_POST['category_description'];
        // $approval_algorithm = $_POST['approval_algorithm'];
        $category_type= $_POST['category_type'];
        // $approvar_1= $_POST['approvar_1'];
        // $approvar_2= $_POST['approvar_2'];
        $category_administrator= isset($_POST['category_administrator']) ? $_POST['category_administrator'] : 0;
        echo $this->Inventory_model_v2->submitProductCategory($category_name, $is_sellable, $category_description, $category_type, $category_administrator);
    }
    
      public function add_sub_category() {
        $subcategory_id = $this->Inventory_model_v2->add_sub_category();
        echo json_encode($subcategory_id);
      }
      
      public function get_categoryWise_subCategories() {
        $category_id = $_POST['category_id'];
        $products = $this->Inventory_model_v2->get_categoryWise_subCategories($category_id);
        $productArray = array();
        if(!empty($products[0])) {
          foreach ($products[0] as $key => $product) {
            if(!array_key_exists($product->id, $productArray)) {
              $productArray[$product->id] = $product;
              $productArray[$product->id]->attributes = json_decode($product->attributes);
              $productArray[$product->id]->items = array();
              $productArray[$product->id]->item_ids= array();
              if(!empty($products[1][$key]->l_m_by))
                $productArray[$product->id]->l_m_by= $products[1][$key]->l_m_by;
            }
            if($product->item_name != null) {
              array_push($productArray[$product->id]->items, $product->item_name);
              array_push($productArray[$product->id]->item_ids, $product->item_id);
            }
          }
        }
        
        $result_array=[];
        foreach($productArray as $key => $val){
          $result_array[]=$val;
        }

        $approversList= $this->Inventory_model_v2->getIndentAndFinancialApprover($category_id);
        $data["result"]=$result_array;
        $data["approversList"] = $approversList;
        echo json_encode($data);
      }
      
      public function get_all_categories() {
        $categories = $this->Inventory_model_v2->get_all_categories();
        $receipt_book = $this->Sales_model_v2->get_sales_receipt_books();
        $recept_format = $this->fee_library->receipt_format_creation($receipt_book);
    
        foreach ($categories as $key => &$val) {
          foreach ($recept_format as $key => $receipt) {
            if ($val->receipt_book_id == $receipt->id) {
              $val->receipt_format = $receipt->receipt;
            }
          }
        }
    
        echo json_encode($categories);
      }
      
      public function add_item(){
        $product_id = $this->input->post('product_id');
        $status = $this->Inventory_model_v2->newProductVarient();
        echo $status;
          
      }
      
      public function delete_sub_category(){
        $id=$_POST['sub_category_id'];
        $result=$this->Inventory_model_v2->delete_sub_category($id);
        echo $result;    
      }

      public function getvariantdetails_edit(){
        $result=$this->Inventory_model_v2->getvariantdetails_edit();
        echo json_encode($result);
      }
      
      public function delete_items(){
        $item_ids_arr= $_POST['item_ids_arr'];
        $result=$this->Inventory_model_v2->delete_items($item_ids_arr);
        
        echo $result;
      }
      
      public function get_items_from_sub_category() {
        $sub_category_id= $_POST['sub_category_id'];
        $result= $this->Inventory_model_v2->get_items_from_sub_category($sub_category_id);
        echo json_encode($result);
      }

    public function item_master(){
      $data['unit_types'] = $this->config->item('procurement_units');
      $data['staffList'] = $this->Inventory_model_v2->getStaffList();
      $data["expenseSubCategories"] = $this->Inventory_model_v2->getExpenseSubCategories();
      $data['main_content'] = 'procurement/inventory_view_v2/item_master';
      $this->load->view('inc/template', $data);
    }

    public function get_information_about_item() {
      $item_id= $_POST['item_id'];
      $result= $this->Inventory_model_v2->get_information_about_item($item_id);
      $result[0]->attributes= json_decode( $result[0]->attributes );
      echo json_encode($result);
    }

    public function get_category_all_details() {
      echo json_encode($this->Inventory_model_v2->get_category_all_details($_POST['item_category_id']));
    }

    public function getAllPossibleReceiptFormat() {
      $receipt_book = $this->Sales_model_v2->get_sales_receipt_books();
      $data['recept_format'] = $this->fee_library->receipt_format_creation($receipt_book);
      echo json_encode( $data['recept_format'] );
    }

    public function saveReceiptFormat () {
      $id= $_POST['id'];
      $cat_id= $_POST['category_id'];
      echo json_encode( $this->Inventory_model_v2->saveReceiptFormat($id, $cat_id) );
    }

    public function submit_receipt_template() {
      echo json_encode( $this->Inventory_model_v2->submit_receipt_template() );
    }

    public function editCategory() {
      // echo '<pre>'; print_r($_POST); die();
      echo json_encode( $this->Inventory_model_v2->editCategory() );
    }

    public function deleteCategory() {
      // echo '<pre>'; print_r($_POST); die();
      echo json_encode( $this->Inventory_model_v2->deleteCategory() );
    }

    public function saveItemInforation() {
      $item_image= $_FILES['i_image'];
      $upload_details= '';
      if($item_image['name']) {
        $upload_details= $this->s3FileUpload($item_image,'procurement');
      }
      echo json_encode( $this->Inventory_model_v2->saveItemInforation($upload_details) );
    }

    public function getProductNames() {
      $category_id = $_POST['category_id'];
      $products = $this->Inventory_model_v2->getProductNames($category_id);
      echo json_encode($products);
    }

    public function getVariantNames() {
      $product_id = $_POST['product_id'];
      $sales_year_id = $_POST['sales_year_id'];
      $variants = $this->Inventory_model_v2->getVariantNames2($product_id, $sales_year_id);
      echo json_encode($variants);
    }

    // public function getVariantNames4() {
    //   $product_id = $_POST['product_id'];
    //   // $sales_year_id = $_POST['sales_year_id'];
    //   $variants = $this->Inventory_model_v2->getVariantNames($product_id);
    //   echo json_encode($variants);
    // }

    public function getVariantNames3() {
      $product_id = $_POST['product_id'];
      $variants = $this->Inventory_model_v2->getVariantNames($product_id);
      echo json_encode($variants);
    }

    public function getTransactionReport() {
      $category_id = $_POST['category_id'];
      $product_id = $_POST['product_id'];
      $variant_id = $_POST['variant_id'];
      $sales_year_id = isset( $_POST['sales_year_id']) ?  $_POST['sales_year_id'] : 0;
      $data = $this->Inventory_model_v2->getTransactionReport($category_id, $product_id, $variant_id, $sales_year_id);
      echo json_encode($data);
    }

    public function allocate_to_staff() { // Tile come from requisition
      $data['staffList'] = $this->Inventory_model_v2->getStaffList();
      $data['categories'] = $this->Inventory_model_v2->get_all_categories('category_admin_only');
      $data['salesYear'] = $this->Inventory_model_v2->get_sales_year();
      $data['main_content'] = 'procurement/inventory_view_v2/allocate_to_staff';
      $this->load->view('inc/template', $data);
    }

    public function allocate_products() {
      $status = $this->Inventory_model_v2->allocate_products();
      if($status){
        $this->session->set_flashdata('flashSuccess', 'Items allocated successfully');
      } else {
        $this->session->set_flashdata('flashError', 'Something went wrong!');
      }
      redirect('procurement/inventory_controller_v2/allocate_to_staff');
    }

    public function inventory_allocations_staff_report() { // Tile come from requisition
      $data['staffList'] = $this->Inventory_model_v2->getStaffList();
      $data['main_content'] = 'procurement/inventory_view_v2/inventory_allocations_staff_report';
      $this->load->view('inc/template', $data);
    }

    public function get_inventory_allocations_staff_report() {
      $input= $this->input->post();
      // echo '<pre>'; print_r($input); die(); 
      $from_date = $input['from_date'];
      $to_date = $input['to_date'];
      $selected_staffs = $input['selected_staffs'];
      $report_type = $input['report_type'];
      $data = $this->Inventory_model_v2->get_inventory_allocations_staff_report($from_date, $to_date, $selected_staffs, $report_type);
      // echo '<pre>'; print_r($data); die(); 
      echo json_encode($data);
    }

    public function collect_from_satff() { // Tile come from requisition
      $data['staffList'] = $this->Inventory_model_v2->getStaffList();
      $data['salesYear'] = $this->Inventory_model_v2->get_sales_year();
      $data['main_content'] = 'procurement/inventory_view_v2/collect_from_satff';
      $this->load->view('inc/template', $data);
    }

    public function get_allocated_items_of_a_staff() {
      $input= $this->input->post();
      $selected_staff_id = $input['selected_staff_id'];
      $data = $this->Inventory_model_v2->get_allocated_items_of_a_staff($selected_staff_id);
      echo json_encode($data);
    }

    public function submit_staff_item_collection() {
      $input= $this->input->post();
      $data = $this->Inventory_model_v2->submit_staff_item_collection($input);
      echo json_encode($data);
    }

    public function get_template_format_by_category_id() {
      $input= $this->input->post();
      $data = $this->Inventory_model_v2->get_template_format_by_category_id($input['category_id']);
      echo json_encode($data);
    }

    public function request_for_items() {
      $data['staffList'] = $this->Inventory_model_v2->getStaffList();
      $data['categories'] = $this->Inventory_model_v2->get_category_category_type_wise('Consumables');
      $data['department'] = $this->Inventory_model_v2->get_requester_department();
      $data['reporting_manager'] = $this->Inventory_model_v2->get_reporting_manager();
      $status_of_items = $this->Inventory_model_v2->requested_items_status(); 

      foreach($status_of_items as $key => $val) {
        if($val->document_url) {
          $val->document_url= $this->filemanager->getFilePath($val->document_url);
        }
      }

      $data['requested_items_status']= $status_of_items;

      // echo '<pre>'; print_r($data['requested_items_status']); die();

      $data['main_content'] = 'procurement/inventory_view_v2/item_request_form';
      $this->load->view('inc/template', $data);
    }

    public function submit_item_request_form() {
      $input= $this->input->post();
      $doc= $_FILES['document'];
      $upload_details= '';
      if($doc['name']) {
        $upload_details= $this->s3FileUpload($doc); // Need to work
      }
      // echo '<pre>'; print_r($doc); die(); 

      $data = $this->Inventory_model_v2->submit_item_request_form($input, $upload_details);
      echo json_encode($data);
    }

    private function s3FileUpload($file, $folder_name = 'escort_auth') {
        if ($file['tmp_name'] == '' || $file['name'] == '') {
        return ['status' => 'empty', 'file_name' => ''];
        }
        return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], $folder_name);
    }

    public function all_requests_hod_wise($filter= 'all') {
      $data['filter']= $filter;
      $data['staffList'] = $this->Inventory_model_v2->getStaffList();
      $status_of_items = $this->Inventory_model_v2->all_requested_form_for_hod($filter);
      foreach($status_of_items as $key => $val) {
        if($val->document_url) {
          $val->document_url= $this->filemanager->getFilePath($val->document_url);
        }
      }

      $data['requested_items_status']= $status_of_items;

      $data['main_content'] = 'procurement/inventory_view_v2/all_requests_hod_wise';
      $this->load->view('inc/template', $data);
    }

    public function change_status_hod_side() {
      $input= $this->input->post();
     
      $data = $this->Inventory_model_v2->change_status_hod_side($input);
      echo json_encode($data);
    }

    public function get_item_list_from_request() {
      $input= $this->input->post();
      $data = $this->Inventory_model_v2->get_item_list_from_request($input['request_master_id']);
      echo json_encode($data);
    }

    // public function save_item_from_request() {
    //   $input= $this->input->post();
    //   $data = $this->Inventory_model_v2->save_item_from_request($input['pri_id'], $input['qty'], $input['proc_request_master_id']);
    //   echo json_encode($data);
    // }

    public function delete_item_from_request() {
      $input= $this->input->post();
      $data = $this->Inventory_model_v2->delete_item_from_request($input['pri_id']);
      echo json_encode($data);
    }

    public function send_modification_acknoledgement() {
      $input= $this->input->post();
      $req_master_id= $input['req_master_id'];
      $req_master_hod_id= $input['req_master_hod_id'];
      $pri_ids= $input['pri_ids'];
      $item_qtys= $input['item_qtys'];
      $data = $this->Inventory_model_v2->save_item_from_request($pri_ids, $item_qtys, $req_master_id);
      echo '1';
    }

    public function all_requests_for_faciliy_department() {
      // $data['staffList'] = $this->Inventory_model_v2->getStaffList();
      $status_of_items = $this->Inventory_model_v2->all_requests_for_faciliy_department();
      foreach($status_of_items as $key => $val) {
        if($val->document_url) {
          $val->document_url= $this->filemanager->getFilePath($val->document_url);
        }
      }

      $data['requested_items_status']= $status_of_items;

      $data['main_content'] = 'procurement/inventory_view_v2/all_requests_for_faciliy_department';
      $this->load->view('inc/template', $data);
    }

    public function change_status_by_facility() {
      $input= $this->input->post();
      $request_master_id= $input['request_master_id'];
      $status_value= $input['status_value'];
      $facility_remarks= $input['facility_remarks'];
      $data = $this->Inventory_model_v2->change_status_by_facility($request_master_id, $status_value, $facility_remarks);
      echo json_encode($data);
    }

    public function allocate_to_staff_by_facility() {
      $item_photo= $_FILES['item_photo'];
      $upload_item_photo= '';
      if($item_photo['name']) {
        $upload_item_photo= $this->s3FileUpload($item_photo); // Need to work
      }

      $signed_photo= $_FILES['signed_photo'];
      $upload_signed_photo= '';
      if($signed_photo['name']) {
        $upload_signed_photo= $this->s3FileUpload($signed_photo); // Need to work
      }

      $input= $this->input->post();
      $data = $this->Inventory_model_v2->allocate_to_staff_by_facility($input, $upload_item_photo, $upload_signed_photo);
      echo json_encode($data);
    }

    public function get_category_category_type_wise() {
      $input= $this->input->post();
      $data = $this->Inventory_model_v2->get_category_category_type_wise($input['input_value']);
      echo json_encode($data);
    }

    public function get_sub_categories_and_items() {
      $input= $this->input->post();
      $data = $this->Inventory_model_v2->get_sub_categories_and_items($input);
      echo json_encode($data);
    }

    public function get_request_full_details() {
      $input= $this->input->post();
      $data= $this->Inventory_model_v2->get_request_full_details($input['request_master_id']);
      if($data->document_url) {
        $data->document_url= $this->filemanager->getFilePath($data->document_url);
      }

      // echo '<pre>'; print_r($data); die();
      echo json_encode($data);
    }

    public function change_status_function() {
      $input= $this->input->post();
      $data = $this->Inventory_model_v2->change_status_function($input);
      echo json_encode($data);
    }

    public function get_history_of_request() {
      $input= $this->input->post();
      $data = $this->Inventory_model_v2->get_history_of_request($input['req_master_id']);

      // echo '<pre>'; print_r($data); die();

      echo json_encode($data);
    }

    public function create_approvals_type_wise() {
      $input= $this->input->post();
      $data = $this->Inventory_model_v2->create_approvals_type_wise($input);
      echo json_encode($data);
    }

    public function get_category_approvals_details() {
      $input= $this->input->post();
      $data = $this->Inventory_model_v2->get_category_approvals_details($input);
      echo json_encode($data);
    }

    public function get_approver_name_time_and_remarks() {
      $input= $this->input->post();
      $data = $this->Inventory_model_v2->get_approver_name_time_and_remarks($input);
      echo json_encode($data);
    }

    public function get_allocation_status() {
      $input= $this->input->post();
      $data = $this->Inventory_model_v2->get_allocation_status($input);

      // echo '<pre>'; print_r($data); die();

      if(!empty($data->allo_status)) {
        foreach($data->allo_status as $key => $val) {
          if($val->allocate_picture_url) {
            $val->allocate_picture_url= $this->filemanager->getFilePath($val->allocate_picture_url);
          }
          if($val->allocate_signed_url) {
            $val->allocate_signed_url= $this->filemanager->getFilePath($val->allocate_signed_url);
          }
        }
      }

      // echo '<pre>'; print_r($data); die();
      
      echo json_encode($data);
    }

    public function get_allocated_items_of_a_request() {
      $input= $this->input->post();
      $selected_staff_id = $input['selected_staff_id'];
      $req_master_id = $input['req_master_id'];
      $data = $this->Inventory_model_v2->get_allocated_items_of_a_request($selected_staff_id, $req_master_id);
      echo json_encode($data);
    }
    
    public function submit_staff_item_collection_by_facility() {
      $input= $this->input->post();
      $data = $this->Inventory_model_v2->submit_staff_item_collection_by_facility($input);
      echo json_encode($data);
    }

    public function activate_deactivate_item() {
      $input= $this->input->post();
      $data = $this->Inventory_model_v2->activate_deactivate_item($input);
      echo json_encode($data);
    }

    public function open_all_item_in_popup() {
      $input= $this->input->post();
      $data = $this->Inventory_model_v2->open_all_item_in_popup($input['sub_category_id']);
      echo json_encode($data);
    }

    public function change_initial_quantity_of_item() {
      $input= $this->input->post();
      $data = $this->Inventory_model_v2->change_initial_quantity_of_item($input);
      echo json_encode($data);
    }

    public function update_price_and_sales_transaction() {
      $input= $this->input->post();
      $data = $this->Inventory_model_v2->update_price_and_sales_transaction($input);
      echo json_encode($data);
    }

    public function calculater_current_quantity_for_each_item() {
      $data = $this->Inventory_model_v2->calculater_current_quantity_for_each_item();
      echo json_encode($data);
    }

    public function item_stock_report() {
      $data['categories'] = $this->Inventory_model_v2->get_all_categories();
      $data['salesYear'] = $this->Inventory_model_v2->get_sales_year();
      $data['main_content'] = 'procurement/inventory_view_v2/item_stock_report_view';
      $this->load->view('inc/template', $data);
    }

    public function get_stock_report() {
      $data = $this->Inventory_model_v2->get_stock_report();
      echo json_encode($data);
    }

    public function get_subcategories() {
      $data = $this->Inventory_model_v2->get_subcategories();
      echo json_encode($data);
    }

    public function update_price_qty_in_invoices() {
      $data = $this->Inventory_model_v2->update_price_qty_in_invoices();
      echo json_encode($data);
    }

    public function validate_sku_asUnique() {
      $data = $this->Inventory_model_v2->validate_sku_asUnique();
      echo json_encode($data);
    }

    public function staff_allocation_widgets() {
      $site_url = site_url();

        $data['request'] = array(
          [
            'title' => 'Staff Request form<br><small style="color: red">(Approvers required if any)</small>',
            'sub_title' => 'Request for items and view all reqests',
            'icon' => 'svg_icons/add.svg',
            'url' => $site_url.'procurement/inventory_controller_v2/request_for_items',
            'permission' => $this->authorization->isAuthorized('PROCUREMENT.REQUISITION')
        ],
        
        );
        $data['request'] = checkTilePermissions($data['request']);

        $data['allocation_collection']= array(
          [
            'title' => 'Allocate to Staff<br><small style="color: red">(Approvers not required)</small>',
            'sub_title' => 'Allocate Inventory to Staff',
            'icon' => 'svg_icons/allocateproducts.svg',
            'url' => $site_url.'procurement/inventory_controller_v2/allocate_to_staff',
            'permission' => $this->authorization->isAuthorized('PROCUREMENT.REQUISITION') && $this->authorization->isModuleEnabled('PROCUREMENT_INVENTORY') && $this->authorization->isAuthorized('PROCUREMENT_INVENTORY.MODULE')
        ],
        [
            'title' => 'Collect from Staff',
            'sub_title' => 'Collect Inventory from Staff',
            'icon' => 'svg_icons/allocateproducts.svg',
            'url' => $site_url.'procurement/inventory_controller_v2/collect_from_satff',
            'permission' => $this->authorization->isAuthorized('PROCUREMENT.REQUISITION') && $this->authorization->isModuleEnabled('PROCUREMENT_INVENTORY') && $this->authorization->isAuthorized('PROCUREMENT_INVENTORY.MODULE')
        ],
        );


        $this->load->model('procurement/Requisition_model_v2');  
        $check_if_staff_is_hod= $this->Requisition_model_v2->check_staff_accessibility();

        if($check_if_staff_is_hod) {
            array_push($data['allocation_collection'], 
                [
                    'title' => 'All Requests (Approvals / Allocations)',
                    'sub_title' => 'All my team reqests',
                    'icon' => 'svg_icons/initsubstitutioncache.svg',
                    'url' => $site_url.'procurement/inventory_controller_v2/all_requests_hod_wise',
                    'permission' => $this->authorization->isAuthorized('PROCUREMENT.REQUISITION')
                ]
            );
        }
        $data['allocation_collection'] = checkTilePermissions($data['allocation_collection']);

        $data['reports'] = array(
          [
            'title' => 'Staff Allocation / Collection Report',
            'sub_title' => 'See the Item stock details',
            'icon' => 'svg_icons/assessment.svg',
            'url' => $site_url.'procurement/inventory_controller_v2/inventory_allocations_staff_report',
            'permission' =>  $this->authorization->isAuthorized('PROCUREMENT.REPORTS')
          ]
        );
        $data['reports'] = checkTilePermissions($data['reports']);

        $data['main_content']    = 'procurement/inventory_view_v2/staff_allocation_widgets';
        $this->load->view('inc/template', $data);
    }

    public function indent_widgets() {
      $site_url = site_url();

        $data['Purchases'] = array(
          [
            'title' => 'Purchases (V1)',
            'sub_title' => 'Apply for assets / inventory',
            'icon' => 'svg_icons/subjects.svg',
            'url' => $site_url.'procurement/requisition_controller_v2/requisition_management',
            'permission' => $this->authorization->isAuthorized('PROCUREMENT.REQUISITION')
          ],
        [
          'title' => 'Manage PO',
          'sub_title' => 'Apply for assets / inventory',
          'icon' => 'svg_icons/subjects.svg',
          'url' => $site_url . 'procurement/requisition_controller_v2/purchase_order_v2',
          'permission' => $this->authorization->isAuthorized('PROCUREMENT.REQUISITION_V2')
        ]
        
        );
        $data['Purchases'] = checkTilePermissions($data['Purchases']);

        // $data['Indent']= array(
          
        // [
        //     'title' => 'Manage Indents',
        //     'sub_title' => 'Manage purchase',
        //     'icon' => 'svg_icons/allocateproducts.svg',
        //     'url' => $site_url . 'procurement/requisition_controller_v2/bill_of_materials',
        //     'permission' => $this->authorization->isAuthorized('INDENT.VIEW_INDENT')
        // ]
        // );
        // $data['Indent'] = checkTilePermissions($data['Indent']);

        $data['main_content']    = 'procurement/inventory_view_v2/indent_widgets';
        $this->load->view('inc/template', $data);
    }

    public function item_master_widgets() {
      $site_url = site_url();

        $data['item_master'] = array(
          
          [
            'title' => 'Inventory Issue',
            'sub_title' => 'Add / Edit / View Items',
            'icon' => 'svg_icons/initsubstitutioncache.svg',
            'url' => $site_url.'procurement/sales_controller_v2/index',
            'permission' => $this->authorization->isAuthorized('PROCUREMENT.REQUISITION') && $this->authorization->isModuleEnabled('PROCUREMENT_INVENTORY') && $this->authorization->isAuthorized('PROCUREMENT_INVENTORY.MODULE')
          ],
          [
            'title' => 'Inventory Staff Allocation',
            'sub_title' => 'Add / Edit / View Items',
            'icon' => 'svg_icons/allocateproducts.svg',
            'url' => $site_url.'procurement/inventory_controller_v2/staff_allocation_widgets',
            'permission' => $this->authorization->isAuthorized('PROCUREMENT.REQUISITION') && $this->authorization->isModuleEnabled('PROCUREMENT_INVENTORY') && $this->authorization->isAuthorized('PROCUREMENT_INVENTORY.MODULE')
        ]
          
        );
        $data['item_master'] = checkTilePermissions($data['item_master']);

        $data['reports']= array(
          
          [
            'title' => 'Item Ledger Report',
            'sub_title' => 'See the Item stock details',
            'icon' => 'svg_icons/assessment.svg',
            'url' => $site_url.'procurement/requisition_controller_v2/inventoty_transaction_report',
            'permission' =>  $this->authorization->isAuthorized('PROCUREMENT.REPORTS')
          ],
          [
            'title' => 'Item Stock Report',
            'sub_title' => 'See the Item stock details',
            'icon' => 'svg_icons/assessment.svg',
            'url' => $site_url.'procurement/inventory_controller_v2/item_stock_report',
            'permission' =>  $this->authorization->isAuthorized('PROCUREMENT.REPORTS')
          ],
          [
            'title' => 'Threshold Report',
            'sub_title' => 'Threshold details',
            'icon' => 'svg_icons/assessment.svg',
            'url' => $site_url.'procurement/inventory_controller_v2/threshold_quantity_report',
            'permission' =>  $this->authorization->isAuthorized('PROCUREMENT.REPORTS')
          ]
        );
        $data['reports'] = checkTilePermissions($data['reports']);

        $data['recocile_item']= array(
          [
              'title' => 'Reconcile Item (Super Admin)',
              'sub_title' => 'Add / Edit / View Vendors',
              'icon' => 'svg_icons/vendormaster.svg',
              'url' => $site_url.'procurement/inventory_controller_v2/super_admin_page',
              'permission' => $this->authorization->isSuperAdmin()
          ],
          [
            'title' => 'Reconcile Report (Super Admin)',
            'sub_title' => 'Add / Edit / View Items stock',
            'icon' => 'svg_icons/vendormaster.svg',
            'url' => $site_url.'procurement/inventory_controller_v2/item_reconciliation_report',
            'permission' => $this->authorization->isSuperAdmin()
        ]
          );
          $data['recocile_item'] = checkTilePermissions($data['recocile_item']);


        $data['main_content']    = 'procurement/inventory_view_v2/item_master_widgets';
        $this->load->view('inc/template', $data);
    }

    public function super_admin_page() {
      $data['salesYear'] = $this->Inventory_model_v2->get_sales_year();

      $data['main_content']    = 'procurement/inventory_view_v2/super_admin_page';
      $this->load->view('inc/template', $data);
    }

    public function easy_update_quantity_super_admin() {
      $data = $this->Inventory_model_v2->easy_update_quantity_super_admin();
      echo json_encode($data);
    }

    public function salesYear_wise_items() {
      $data = $this->Inventory_model_v2->salesYear_wise_items();
      echo json_encode($data);
    }

    public function get_tx_details_of_item() {
      $data = $this->Inventory_model_v2->get_tx_details_of_item();
      echo json_encode($data);
    }

    function finish_reconciling() {
      $data = $this->Inventory_model_v2->finish_reconciling();
      echo json_encode($data);
    }

    function finish_allocation_first() {
      $data = $this->Inventory_model_v2->finish_allocation_first();
      echo json_encode($data);
    }

    function close_reconcillation_item() {
      $data = $this->Inventory_model_v2->close_reconcillation_item();
      echo json_encode($data);
    }

    function get_missmatch_data_entry_invoices() {
      $data = $this->Inventory_model_v2->get_missmatch_data_entry_invoices();
      echo json_encode($data);
    }

    public function threshold_quantity_report() {
      $data['categories'] = $this->Inventory_model_v2->get_all_categories();
      $data['salesYear'] = $this->Inventory_model_v2->get_sales_year();
      $data['main_content'] = 'procurement/inventory_view_v2/threshold_quantity_report';
      $this->load->view('inc/template', $data);
    }

    public function get_threshold_report() {
      $data = $this->Inventory_model_v2->get_threshold_report();
      echo json_encode($data);
    }

    public function parent_reserved_items(){
      $data['item_category'] = $this->Inventory_model_v2->get_category_items();
      $data['main_content'] = 'product/parent_reserved_items';
      $this->load->view('inc/template', $data);
    }

    public function upload_category_image_path(){
      $subCatId = $_POST['subCatId'];
      $path = $_POST['path'];
      echo $this->Inventory_model_v2->upload_category_image_path_byId($subCatId, $path);

    }

    public function get_ordered_items(){
      $data = $this->Inventory_model_v2->get_ordered_items($_POST);
      echo json_encode($data);
    }

    public function parent_orders_summary_report(){
      $data['item_category'] = $this->Inventory_model_v2->get_category_items();
      $data['main_content'] = 'product/parent_orders_summary_report_view';
      $this->load->view('inc/template', $data);
    }

    public function get_parent_ordered_items(){
      $data = $this->Inventory_model_v2->get_parent_ordered_items($_POST);
      echo json_encode($data);
    }

    public function make_sub_category_visible_to_parents(){
      $data = $this->Inventory_model_v2->make_sub_category_visible_to_parents($_POST['sub_category_id'],$_POST['button_status']);
      echo json_encode($data);
    }

    public function enable_disable_custom_name(){
      $data = $this->Inventory_model_v2->enable_disable_custom_name($_POST['subcat_id'],$_POST['button_status']);
      echo json_encode($data);
    }

    public function getStaffForIndentApproval(){
      echo json_encode($this->Inventory_model_v2->getStaffForIndentApproval($this->input->post()));
    }

    public function getStaffForFinancialApproval(){
      echo json_encode($this->Inventory_model_v2->getStaffForFinancialApproval($this->input->post()));
    }

    function onclick_for_final_reconcile() {
      $data = $this->Inventory_model_v2->onclick_for_final_reconcile();
      echo json_encode($data);
    }

    function return_collect_reconcile() {
      // $data = $this->Inventory_model_v2->return_collect_reconcile();
      $data = $this->Inventory_model_v2->return_collect_and_delete_reconcile();
      echo json_encode($data);
    }

    public function indent_widgets_v2() {
      $site_url = site_url();

        // $data['Purchases'] = array(
        //   [
        //     'title' => 'Purchases',
        //     'sub_title' => 'Apply for assets / inventory',
        //     'icon' => 'svg_icons/subjects.svg',
        //     'url' => $site_url.'procurement/requisition_controller_v2/requisition_management',
        //     'permission' => $this->authorization->isAuthorized('PROCUREMENT.REQUISITION')
        //   ],
        // [
        //   'title' => 'Purchase Order v2',
        //   'sub_title' => 'Apply for assets / inventory',
        //   'icon' => 'svg_icons/subjects.svg',
        //   'url' => $site_url . 'procurement/requisition_controller_v2/purchase_order_v2',
        //   'permission' => $this->authorization->isSuperAdmin()
        // ]
        
        // );
        // $data['Purchases'] = checkTilePermissions($data['Purchases']);

        $data['indent']= array(
          [
              'title' => 'Manage Indents',
              'sub_title' => 'Manage purchase',
              'icon' => 'svg_icons/allocateproducts.svg',
              'url' => $site_url . 'procurement/requisition_controller_v2/indents',
              'permission' => $this->authorization->isAuthorized('INDENT.VIEW_INDENT')
          ]
        );

      $data['indentReports'] = array(
        [
          'title' => 'Indent Summary Report',
          'sub_title' => 'This report is going to be used across departments and for decision-making: How much of the indent is used, Vendor selected, Status at various stages (approval, quotation) and etc',
          'icon' => 'svg_icons/allocateproducts.svg',
          'url' => $site_url . 'procurement/requisition_controller_v2/indent_summary_report',
          'permission' => $this->authorization->isSuperAdmin()
        ]
      );

        $data['indent'] = checkTilePermissions($data['indent']);
        $data['indentReports'] = checkTilePermissions($data['indentReports']);

        $data['main_content']    = 'procurement/inventory_view_v2/indent_widgets_v2';
        $this->load->view('inc/template', $data);
    }

    function delivery_management() {
      if(!$this->authorization->isModuleEnabled('PROCUREMENT_BUDGET') || !$this->authorization->isAuthorized('PROCUREMENT_BUDGET.MODULE')) {
          redirect('dashboard', 'refresh');
      } else {
          $data['main_content']    = 'procurement/inventory_view_v2/delivery_management_dashboard';
          $this->load->view('inc/template', $data);
      }
  }

  public function administration_dashboard() {
    $site_url = site_url();

      $data['administration'] = array(
        [
          'title' => 'Procurement Sales Year',
          'sub_title' => 'Sales Year',
          'icon' => 'svg_icons/vendormaster.svg',
          'url' => $site_url.'procurement/requisition_controller_v2/procurement_sales_year',
          'permission' => $this->authorization->isAuthorized('PROCUREMENT.REQUISITION') && $this->authorization->isModuleEnabled('PROCUREMENT_INVENTORY') && $this->authorization->isAuthorized('PROCUREMENT_INVENTORY.MODULE')
        ]
      );
      $data['administration'] = checkTilePermissions($data['administration']);

      $data['main_content']    = 'procurement/inventory_view_v2/administration_dashboard';
      $this->load->view('inc/template', $data);
  }

  function item_reconciliation_report() {
    // $data['categories'] = $this->Inventory_model_v2->get_all_categories();
    $data['salesYear'] = $this->Inventory_model_v2->get_sales_year();
    $data['main_content'] = 'procurement/inventory_view_v2/item_reconciliation_report';
    $this->load->view('inc/template', $data);
  }

  function getItemsSalesYearWise() {
    $data = $this->Inventory_model_v2->getItemsSalesYearWise();
    echo json_encode($data);
  }

  function getReconciliationStatus() {
    $data = $this->Inventory_model_v2->getReconciliationStatus();
    echo json_encode($data);
  }

  function finish_reconciliation() {
      $data = $this->Inventory_model_v2->finish_reconciliation();
      echo json_encode($data);
  }

    function getReconciliationStatusOfAnItem() {
      $data = $this->Inventory_model_v2->getReconciliationStatusOfAnItem();
      echo json_encode($data);
    }

  public function service_delivery_management(){
    $data['service_delivery_challan'] = array(
      [
        'title' => 'Service Delivery Challans',
        'sub_title' => 'Service Delivery Challans',
        'icon' => 'svg_icons/allocateproducts.svg',
        'url' => site_url() . 'procurement/requisition_controller_v2/service_delivery_challans',
        'permission' => $this->authorization->isAuthorized('INDENT.VIEW_INDENT')
      ]
    );

    $data['main_content'] = 'procurement/inventory_view_v2/service_delivery_challan_widgets';
    $this->load->view('inc/template', $data);
  }

  function map_with_expense_subcategory() {
    $input= $this->input->post();
    $data = $this->Inventory_model_v2->map_with_expense_subcategory($input);
    echo json_encode($data);
  }

}
?>