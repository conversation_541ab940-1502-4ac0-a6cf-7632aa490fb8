<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2'); ?>">Procurement</a></li>
    <li><a href="<?php echo site_url('procurement/inventory_controller_v2/indent_widgets_v2'); ?>">Indent Management</a>
    </li>
    <li>Indent Summary Report</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="panel-header"
                style="margin: 0px; background: none; border-bottom: 1px solid lightgray; height: 3.7rem;">
                <h3>
                    <a style="" class="back_anchor"
                        href="<?php echo site_url('procurement/inventory_controller_v2/indent_widgets_v2') ?>"
                        class="control-primary">
                        <span class="fa fa-arrow-left"></span>
                    </a>
                    Indent Summary Report
                </h3>
            </div>
        </div>
        <div class="col-md-12">
            <div class="purchase-container">
                <div class="row mb-5">
                    <div class="col-md-2 form-group">
                        <label class="control-label">Date Range</label>
                        <div id="reportrange" class="dtrange" style="width: 100%">
                            <span></span>
                            <input type="hidden" id="from_date">
                            <input type="hidden" id="to_date">
                        </div>
                    </div>

                    <div class="col-md-2 form-group">
                        <label class="control-label">Indent Type</label>
                        <select title="All" class="form-control selectpicker" name="indent_type" id="indent_type">
                            <?php
                            if (empty($item_master_categories)) {
                                echo '<option value="-1">No category found</option>';
                            } else {
                                $indentTypes = [];
                                echo '<option value="All" selected>All</option>';
                                foreach ($item_master_categories as $key => $itemMaster) {
                                    if (!in_array($itemMaster->category_type, $indentTypes)) {
                                        $indentTypes[] = $itemMaster->category_type;
                                        echo '<option value="' . $itemMaster->category_type . '">' . $itemMaster->category_type . '</option>';
                                    }
                                }
                            }
                            ?>
                        </select>
                    </div>

                    <div class="col-md-2 form-group">
                        <label class="control-label">Indent Status</label>
                        <select title="All" class="form-control selectpicker" name="indent_status[]" id="indent_status"
                            multiple>
                            <option value="0">Pending</option>
                            <option value="2">Rejected</option>
                            <option value="4">Pre-Indent Approval Pending</option>
                            <option value="3">Pre-Indent Modification Pending</option>
                            <option value="6">Pre-Indent Approved</option>
                            <option value="5">Quotation Approval Pending</option>
                            <option value="7">Quotation Modification Pending</option>
                            <option value="1">Indent Approved</option>
                        </select>
                    </div>

                    <div class="col-md-2 form-group">
                        <label class="control-label">Item Master Category</label>
                        <select title="All" class="form-control selectpicker" name="item_master_category"
                            id="item_master_category">
                            <?php
                            if (empty($item_master_categories)) {
                                echo '<option value="-1">No category found</option>';
                            } else {
                                echo '<option value="All" selected>All</option>';
                                foreach ($item_master_categories as $key => $itemMaster) {
                                    echo '<option value="' . $itemMaster->id . '">' . $itemMaster->category_name . '</option>';
                                }
                            }
                            ?>
                        </select>
                    </div>

                    <div class="col-md-2 form-group">
                        <label class="control-label">Staff Department</label>
                        <select title="All" class="form-control selectpicker" name="staff_dept" id="staff_dept">
                            <?php
                            if (empty($departments)) {
                                echo '<option value="-1">No depart found</option>';
                            } else {
                                echo '<option value="All" selected>All</option>';
                                foreach ($departments as $key => $dept) {
                                    echo '<option value="' . $dept->id . '">' . $dept->department_name . '</option>';
                                }
                            }
                            ?>
                        </select>
                    </div>

                    <div class="col-md-2 d-flex align-items-end" style="height: 4.4rem;">
                        <button onclick="getIndentSummary()" class="btn btn-dark" style="width: 120px;">
                            <i class="fa fa-file-alt"></i> Get Summary
                        </button>
                    </div>
                </div>
                <div class="indent-summary-table">
                    <div style="color: #333; text-align: center; padding: 2rem;">
                        <i class="fa fa-spinner fa-spin" style="font-size:2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $this->load->view("procurement/purchase_management/indent_reports/utils.php") ?>

<script type="text/javascript">
    $(document).ready(function () {
        getIndentSummary();
    });

    function getIndentSummary() {
        // Disable the button and show loading
        const $btn = $("button[onclick='getIndentSummary()']");
        $btn.prop('disabled', true).html('Please wait...');
        $(".indent-summary-table").html(
            `<div style="color: #333; text-align: center; padding: 2rem;">
                <i class="fa fa-spinner fa-spin" style="font-size:2rem;"></i>
            </div>`
        );

        const fromDate = $("#from_date").val();
        const toDate = $("#to_date").val();
        const indentType = $("#indent_type").val();
        const indentStatus = $("#indent_status").val() || "All";
        const itemMasterCategory = $("#item_master_category").val();
        const staffDept = $("#staff_dept").val();

        $.ajax({
            url: "<?php echo site_url('procurement/Requisition_controller_v2/getIndentSummary') ?>",
            type: "POST",
            data: {
                fromDate,
                toDate,
                indentType,
                indentStatus,
                itemMasterCategory,
                staffDept
            },
            success: function (res) {
                try {
                    res = JSON.parse(res);

                    let html='';

                    if (res.length > 0) {
                        html = `
                            <div style="overflow-x:auto;">
                            <table class="table table-bordered" id="indentSummaryReportDT" style="width: 100%;white-space: nowrap;">
                                <thead style="background: #f2f2f2;">
                                    <tr>
                                        <th>#</th>
                                        <th>Indent ID</th>
                                        <th>Indent Name</th>
                                        <th>Indent Type</th>
                                        <th>Department</th>
                                        <th>Created By</th>
                                        <th>Created On</th>
                                        <th>Indent Amount (₹)</th>
                                        <th>Status</th>
                                        <th>Total Items</th>
                                        <th># Quotations Received</th>
                                        <th>Selected Vendor</th>
                                        <th>% Indent Used</th>
                                    </tr>
                                </thead>
                                <tbody>
                            `;

                        res.forEach((row, index) => {
                            html += `
                            <tr>
                                <td>${++index}</td>
                                <td>${row['indent_id'] ?? 'NA'}</td>
                                <td>${(row['indent_name'] ?? 'NA').toString().length > 40 ? (row['indent_name'].substring(0, 40) + '...') : (row['indent_name'] ?? 'NA')}</td>
                                <td>${row['indent_type'] ?? 'NA'}</td>
                                <td>${row['department'] ?? 'NA'}</td>
                                <td>${row['created_by'] ?? 'NA'}</td>
                                <td>${row['created_on'] ?? 'NA'}</td>
                                <td>${row['indent_amount'] != null && row['indent_amount'] !== '' ? '&#8377;' + row['indent_amount'] : 'NA'}</td>
                                <td>${row['status'] ?? 'NA'}</td>
                                <td>${row['total_items'] ?? 'NA'}</td>
                                <td>${row['quotations_received'] ?? 'NA'}</td>
                                <td>${(row['selected_vendor'] ?? 'NA').toString().length > 40 ? (row['selected_vendor'].substring(0, 40) + '...') : (row['selected_vendor'] ?? 'NA')}</td>
                                <td>${row['percent_indent_used'] ?? 'NA'}</td>
                            </tr>
                        `;
                        });

                        html += `</tbody></table></div>`;
                    } else {
                        html += `${generateMessageHelper("No data found")}`;
                    }

                    $(".indent-summary-table").html(html); // replace existing content

                    if (res.length > 0) {
                        initDataTable("indentSummaryReportDT");
                    }
                } catch (e) {
                    console.error("Parsing error: ", e);
                    $(".indent-summary-table").html(
                        `<div style="color: red; text-align: center;">Error loading summary report</div>`
                    );
                }
                // Re-enable the button
                $btn.prop('disabled', false).html('<i class="fa fa-file-alt"></i> Get Summary');
            },
            error: function () {
                $(".indent-summary-table").html(
                    `<div style="color: red; text-align: center;">Server error occurred</div>`
                );
                // Re-enable the button
                $btn.prop('disabled', false).html('<i class="fa fa-file-alt"></i> Get Summary');
            }
        });
    }

</script>