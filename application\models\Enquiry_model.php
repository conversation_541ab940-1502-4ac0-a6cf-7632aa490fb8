<?php

class Enquiry_model extends CI_Model {
  public function __construct() {
    parent::__construct();
  }

  public function Kolkata_datetime(){
    $timezone = new DateTimeZone("Asia/Kolkata" );
    $date = new DateTime();
    $date->setTimezone($timezone );
    $dtobj = $date->format('Y-m-d H:i:s');
    return $dtobj;
  }

  public function count_no_of_enquiries(){
    $yearId = $this->acad_year->getAcadYearID();
    return $this->db_readonly->select('count(*) as count')->where('academic_year',$yearId)->get('enquiry')->row();
  }

  public function get_status_list_from_table() {
    return $this->db_readonly->select('*')->order_by('process_order')->get('enquiry_internal_status_map')->result();
  }

  public function getTodaysData() {
    $yearId = $this->acad_year->getAcadYearID();
    $data = array();
    $today = date('Y-m-d');

    $date = new DateTime("now");

    $curr_date = $date->format('Y-m-d ');

    $sql = "select count(e.id) as pending from enquiry e where e.next_follow_date <= '$curr_date' and e.status = 'Follow-up required' and e.academic_year= $yearId"; 
    $data['pending_followups'] = $this->db_readonly->query($sql)->row()->pending;

    $overdue = $this->db_readonly->select("count(e.id) as overdeu_followup")
    ->from('enquiry e')
    ->where('e.academic_year',$yearId)
    ->where("e.status = 'Follow-up required'")
    ->where("DATE_FORMAT(e.next_follow_date, '%Y-%m-%d')<='$today'")
    ->get()->row();
   
    $data['overdeu_followup'] = $overdue->overdeu_followup;
    
    $sql1 = "select count(e.id) as active from enquiry e where e.status = 'Follow-up required' and e.academic_year= $yearId"; 

    $data['active_followups'] = $this->db_readonly->query($sql1)->row()->active;
    $data['unassigned'] = $this->db_readonly->select("count(e.id) as unassigned")
    ->from('enquiry e')
    ->where('e.academic_year',$yearId)
    ->where('e.assigned_to is null')
    ->get()->row()->unassigned;
    $data['enquiries_today'] = $this->db_readonly->select("count(e.id) as enquiries_today")
    ->from('enquiry e')
    ->where('e.academic_year',$yearId)
    ->where("DATE_FORMAT(e.created_on, '%Y-%m-%d')='$today'")
    ->get()->row()->enquiries_today;

    $monday = $today;
    if(date('D') != 'Mon') {
      $monday = date('Y-m-d',strtotime("last monday"));
    }
    $data['enquiries_this_week'] = $this->db_readonly->select("count(e.id) as this_week")
    ->from('enquiry e')
    ->where('e.academic_year',$yearId)
    ->where("(DATE_FORMAT(e.created_on, '%Y-%m-%d')>='$monday' and DATE_FORMAT(e.created_on, '%Y-%m-%d')<='$today')")
    ->get()->row()->this_week;

    $data['total_enquiries'] = $this->db_readonly->select('count(id) as total')->where('academic_year',$yearId)->get('enquiry')->row()->total;

    $data['active_enquiries'] = $this->db_readonly->select('count(id) as active')->where('academic_year',$yearId)->where("status!='Closed-not interested'")->get('enquiry')->row()->active;
    
    $data['processed'] = $this->db_readonly->select('count(id) as processed')->where('academic_year',$yearId)->where("status='Processed for admission'")->get('enquiry')->row()->processed;

    $data['activity_today'] = $this->db_readonly->select('count(fu.id) as activity_today')
    ->from('follow_up fu')
    ->where('fu.follow_up_type','Enquiry')
    ->where('date_format(fu.created_on,"%Y-%m-%d")',$today)
    ->where("fu.source_id in (select id from enquiry e where e.academic_year=$yearId)")
    ->get()->row()->activity_today;

    return $data;
  }

  public function getTodaysData_new(){
    $yearId = $this->acad_year->getAcadYearID();
    $data = array();
    $today = date('Y-m-d');

    $date = new DateTime("now");

    $curr_date = $date->format('Y-m-d ');
    $query = "select count(e.id) as total from enquiry e
    join enquiry_internal_status_map es on trim(es.user_status) = trim(e.status)
    where es.reporting_status != 'invalid'
    and e.academic_year = $yearId";

    $data['total_enquiries'] = $this->db->query($query)->row()->total;

    // $data['total_enquiries'] =  $this->db_readonly->select('count(e.id) as total')
    // ->from('enquiry e')
    // ->join('enquiry_internal_status_map es','es.user_status=e.status')
    // ->where('es.reporting_status !=','invalid')
    // ->where('e.academic_year',$yearId)
    // ->get()->row()->total;

    $data['unassigned'] = $this->db_readonly->select("count(e.id) as unassigned")
    ->from('enquiry e')
    ->join('enquiry_internal_status_map es','trim(es.user_status) = trim(e.status)')
    ->where('e.academic_year',$yearId)  
    ->where('(e.assigned_to is null OR e.assigned_to = 0)')
    ->where('es.reporting_status !=','invalid')
    ->get()->row()->unassigned;

    $monday = $today;
    if(date('D') != 'Mon') {
      $monday = date('Y-m-d',strtotime("last monday"));
    }
    $data['enquiries_today'] = $this->db_readonly->select("count(e.id) as enquiries_today")
    ->from('enquiry e')
    ->where('e.academic_year',$yearId)
    ->where("DATE_FORMAT(e.created_on, '%Y-%m-%d')='$today'")
    ->get()->row()->enquiries_today;

    $data['enquiries_this_week'] = $this->db_readonly->select("count(e.id) as this_week")
    ->from('enquiry e')
    ->where('e.academic_year',$yearId)
    ->where("(DATE_FORMAT(e.created_on, '%Y-%m-%d')>='$monday' and DATE_FORMAT(e.created_on, '%Y-%m-%d')<='$today')")
    ->get()->row()->this_week;

    
    $active_data = $this->db_readonly->select("count(e.id) as active")
    ->from('enquiry e')
    ->join('enquiry_internal_status_map es','es.user_status = e.status')
    ->where('e.academic_year',$yearId)
    ->where_in('es.reporting_status','wip')
    ->get()->row();

    $data['active_enquiries'] = $active_data->active;

    $pending = $this->db_readonly->select("count(e.id) as pending_followups")
    ->from('enquiry e')
    ->join('enquiry_internal_status_map es','es.user_status = e.status')
    ->where('e.academic_year',$yearId)
    ->where("e.status = 'Follow-up required'")
    ->get()->row();
   
    $data['pending_followups'] = $pending->pending_followups;

    $overdue = $this->db_readonly->select("count(e.id) as overdeu_followup")
    ->from('enquiry e')
    ->where('e.academic_year',$yearId)
    ->where("e.status = 'Follow-up required'")
    ->where("DATE_FORMAT(e.next_follow_date, '%Y-%m-%d')<='$today'")
    ->get()->row();
   
    $data['overdeu_followup'] = $overdue->overdeu_followup;

    $processed_data = $this->db_readonly->select("count(e.id) as processed_count")
    ->from('enquiry e')
    ->join('enquiry_internal_status_map es','es.user_status = e.status')
    ->where('e.academic_year',$yearId)
    ->where_in('es.reporting_status','convert')
    ->get()->row();

    $data['processed'] = $processed_data->processed_count;

    $data['activity_today'] = $this->db_readonly->select('count(fu.id) as activity_today')
    ->from('follow_up fu')
    ->where('fu.follow_up_type','Enquiry')
    ->where('date_format(fu.created_on,"%Y-%m-%d")',$today)
    ->where("fu.source_id in (select id from enquiry e where e.academic_year=$yearId)")
    ->get()->row()->activity_today;

    return $data;
  }

  public function get_pending_follow_ups_till_today(){
    $yearId = $this->acad_year->getAcadYearID();
    $date = new DateTime("now");

    $curr_date = $date->format('Y-m-d ');

    return $this->db_readonly->select("e.*, sm.id as staffId, concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as name, c.class_name as class")
    ->from('enquiry e')
    ->where('e.academic_year',$yearId)
    ->where('e.status','Follow-up required')
    ->join('follow_up fu',"e.id=fu.source_id and fu.follow_up_type='Enquiry'")
    ->group_by('fu.source_id')
    ->where('fu.next_follow_date is not null')
    ->where('fu.next_follow_date <= "'.$curr_date. '"')
    ->join('class c','e.class_name=c.id','left')
    ->join('staff_master sm','sm.id=e.assigned_to','left')
    ->get()->result();
  }

  public function get_pending_follow_ups_next_week(){
    $date = new DateTime("now");

    $curr_date = $date->format('Y-m-d ');

    return $this->db_readonly->select("e.*, sm.id as staffId, concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as name, c.class_name as class")
    ->from('enquiry e')
    ->where('e.status','Follow-up required')
    ->join('follow_up fu',"e.id=fu.source_id and fu.follow_up_type='Enquiry'")
    ->group_by('fu.source_id')
    ->where('fu.next_follow_date is not null')
    ->where('fu.next_follow_date > "'.$curr_date. '"')
    ->join('class c','e.class_name=c.id','left')
    ->join('staff_master sm','sm.id=e.assigned_to','left')
    ->get()->result();
  }

  public function get_enquiry_all(){
    return $this->db_readonly->select('e.*, c.class_name, sm.first_name, sm1.first_name as counselor')
    ->from('enquiry e')
    ->join('class c','e.class_name=c.id','left')
    ->join('avatar a','a.id=e.created_by','left')
    ->join('staff_master sm','sm.id=a.stakeholder_id','left')
    ->join('staff_master sm1','sm1.id=e.assigned_to','left')
    ->order_by('e.id','desc')
    ->get()->result();
  }

  public function get_enquiry_follow_date($follow_ups,$counselor,$follow_up_status){
    $yearId = $this->acad_year->getAcadYearID();
    $this->db_readonly->select('e.*, c.class_name, sm1.first_name as counselor');
    $this->db_readonly->from('enquiry e');
    if ($follow_ups) {
      if($follow_ups == 'latest_50') {
        $this->db_readonly->limit(50);
      } else {
        $dates = explode('_', $follow_ups);
        $fromDate = $dates[0];
        $toDate = $dates[1];
        $this->db_readonly->where('e.status','Follow-up required');
        $this->db_readonly->join('follow_up fu',"e.id=fu.source_id and fu.follow_up_type='Enquiry'");
        $this->db_readonly->group_by('fu.source_id');
        $this->db_readonly->where('fu.next_follow_date is not null');
        $this->db_readonly->where('date_format(fu.next_follow_date,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
    }
    if ($counselor) {
      if ($counselor == -1) {
        $this->db_readonly->where('e.assigned_to is null');
      }else{
         $this->db_readonly->where('e.assigned_to',$counselor);
      }
    
    }
    if ($follow_up_status) {
     $this->db_readonly->where('e.status',$follow_up_status);
    }
    $this->db_readonly->where('e.academic_year',$yearId);
    $this->db_readonly->join('class c','e.class_name=c.id','left');
    $this->db_readonly->join('staff_master sm1','sm1.id=e.assigned_to','left');
    $this->db_readonly->order_by('e.id','desc');
    return  $this->db_readonly->get()->result();
  }

  public function get_enquiry_follow_data_v2($createdfrom_date, $createdto_date, $followupfrom_date, $followupto_date, $counselor, $follow_up_status, $grade, $leadStatus, $source){
      $yearId = $this->acad_year->getAcadYearID();
      $table = 'class';
      if ($this->settings->getSetting('custom_class_from_enquiry_class_table')) {
          $table = 'enquiry_class';
      }

      $this->db_readonly->select("
          e.id as id, 
          e.student_name, 
          e.parent_name, 
          e.mobile_number, 
          e.email, 
          e.status, 
          DATE_FORMAT(e.created_on, '%d-%m-%Y') as createddate, 
          DATE_FORMAT(e.created_on, '%D %b') as created_on, 
          c.class_name, 
          IFNULL(sm1.first_name, 'Not Assigned') as counselor, 
          DATE_FORMAT(e.next_follow_date, '%d-%m-%Y') as next_follow_date, 
          DATE_FORMAT(e.next_follow_date, '%D %b') as followup_date_display, 
          e.enquiry_number, 
          e.lead_status, 
          eism.reporting_status, 
          eism.color_code, 
          e.father_phone_number, 
          e.mother_phone_number, 
          (CASE 
              WHEN e.mobile_number IS NOT NULL THEN e.mobile_number  
              WHEN e.father_phone_number IS NOT NULL THEN e.father_phone_number 
              WHEN e.mother_phone_number IS NOT NULL THEN e.mother_phone_number 
              ELSE ' ' 
          END) as en_number, 
          COUNT(fu.source_id) as followup_count
      ");

      $this->db_readonly->from('enquiry e');
      $this->db_readonly->join('follow_up fu', "e.id = fu.source_id AND fu.follow_up_type = 'Enquiry'", 'left');
      $this->db_readonly->join('enquiry_internal_status_map eism', 'eism.user_status = e.status', 'left');

      if ($counselor) {
          if ($counselor[0] == -1) {
              $this->db_readonly->where('e.assigned_to IS NULL OR e.assigned_to = 0');
          } else {
              $this->db_readonly->where_in('e.assigned_to', $counselor);
          }
      }

      if ($follow_up_status) {
          $this->db_readonly->where_in('e.status', $follow_up_status);
      }

      if ($createdfrom_date && $createdto_date) {
          $createdfrom_date = date('Y-m-d', strtotime($createdfrom_date));
          $createdto_date = date('Y-m-d', strtotime($createdto_date));
          $this->db_readonly->where("DATE(e.created_on) BETWEEN '$createdfrom_date' AND '$createdto_date'");
      }

      if ($followupfrom_date && $followupto_date) {
          $followupfrom_date = date('Y-m-d', strtotime($followupfrom_date));
          $followupto_date = date('Y-m-d', strtotime($followupto_date));
          $this->db_readonly->where("DATE(e.next_follow_date) BETWEEN '$followupfrom_date' AND '$followupto_date'");
      }

      if ($leadStatus) {
          $this->db_readonly->where('e.lead_status', $leadStatus);
      }

      if ($source) {
          $this->db_readonly->where('e.source', $source);
      }

      $this->db_readonly->join($table . ' c', 'e.class_name = c.id', 'left');
      
      if ($grade) {
          $this->db_readonly->where_in('c.id', $grade);
      }

      $this->db_readonly->where('e.academic_year', $yearId);
      $this->db_readonly->join('staff_master sm1', 'sm1.id = e.assigned_to', 'left');

      // Ensure followup_count is counted per student
      $this->db_readonly->group_by('e.id');
      
      $this->db_readonly->order_by('e.id', 'desc');
      
      return $this->db_readonly->get()->result();
  }

  public function get_follow_up_required_dates(){
    return $this->db_readonly->select("distinct(MAX(date_format(fu.next_follow_date,'%d-%m-%Y'))) as nextFollowDate")
    ->from('enquiry e')
    ->where('e.status','Follow-up required')
    ->join('follow_up fu',"e.id=fu.source_id and fu.follow_up_type='Enquiry'")
    ->group_by('fu.source_id')
    ->where('fu.next_follow_date is not null')
    ->get()->result();
  }

  public function get_couselor_list(){
    return $this->db_readonly->distinct()->select("sm.id as staffId, concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as name")
    ->from('enquiry e')
    ->join('staff_master sm','e.assigned_to=sm.id')
    ->get()->result();
  }

  public function get_couselor_list_search(){
    return $this->db_readonly->distinct()->select("a.id as avatarId, concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as name, sm.id as smId")
    ->from('follow_up efu')
    ->where('efu.follow_up_type','Enquiry')
    ->join('avatar a','a.id=efu.created_by','left')
    ->join('staff_master sm','sm.id=a.stakeholder_id')
    ->order_by('efu.id','desc')
    ->get()->result();
  }

  public function get_staff_list_search(){
    return $this->db_readonly->select("sm.id as smId, concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as name")
    ->from('staff_master sm')
    ->where('sm.status',2)
    ->get()->result();
  }

  public function get_counselor_load(){
    $yearId = $this->acad_year->getAcadYearID();
    $where = "e.academic_year = $yearId";
    $where1 = "e.academic_year = $yearId and (assigned_to is null or assigned_to = 0)";
    $groupBy = "assigned_to is null or assigned_to = '0'";
    $assignedresult =  $this->db_readonly->select("(case when sm.first_name != '' then  concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) else 'Unassigned' end) as name, count(e.id) as count")
    ->from('enquiry e')
    ->where($where)
    ->join('staff_master sm','e.assigned_to=sm.id')
    ->group_by('e.assigned_to')
    ->order_by('sm.first_name','asc')
    ->get()->result();
    $Unassignedresult =  $this->db_readonly->select(" 'Unassigned' as name, count(e.id) as count")
    ->from('enquiry e')
    ->where($where1)
    ->group_by($groupBy)
    ->get()->result();
    $merge = array_merge($assignedresult, $Unassignedresult);
    return $merge;
  }

  public function get_enquiry_next_follow_date($nDate){
    return $this->db_readonly->select('e.*, c.class_name, sm.first_name, sm1.first_name as counselor')
    ->from('enquiry e')
    ->where('e.status','Follow-up required')
    ->join('follow_up fu',"e.id=fu.source_id and fu.follow_up_type='Enquiry'")
    ->where('fu.next_follow_date',$nDate)
    ->join('class c','e.class_name=c.id','left')
    ->join('avatar a','a.id=e.created_by','left')
    ->join('staff_master sm','sm.id=a.stakeholder_id','left')
    ->join('staff_master sm1','sm1.id=e.assigned_to','left')
    ->order_by('e.id','desc')
    ->get()->result();

  }

  public function get_staff_all(){
    $this->db_readonly->select("sm.id as staff_id, concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as name");
    $this->db_readonly->from('staff_master sm');
    $this->db_readonly->where('sm.status','2');
    $this->db_readonly->order_by('sm.first_name');
    return $this->db_readonly->get()->result();
  }

  public function update_staff_id_enquiry($staffId,$enquiryId){
    return $this->db->where('id',$enquiryId)->update('enquiry',array('assigned_to' =>$staffId));
  }

  public function get_inserted_source(){
     return $this->db_readonly->distinct()->select("source")->from('enquiry e')->get()->result();
  }
  public function insert_enquiry_data(){
    $input = $this->input->post();
    $input['student_name'] = (isset($input['student_name']) == '')? '' : $input['student_name'];
    $input['parent_name'] = (isset($input['parent_name']) == '')? '' : $input['parent_name'];
    
    $known_by = (isset($input['how_to_know']) =='')? NULL : $input['how_to_know'];
    $additional_education_needs_details = (isset($input['additional_education_needs_name']) =='')? NULL : $input['additional_education_needs_name'];

    // $sibling_in = (isset($input['sibling_in']) =='')? NULL : $input['sibling_in'];

   
    if($known_by == 'Others') {
      $known_by = $input['known_by'];
    }
    if ($additional_education_needs_details == 'Yes') {
      $additional_education_needs_details = $input['additional_education_needs_details'];
    }
    $sibling_detail  = '';
    if(isset($input['has_sibling'])){
      if($input['has_sibling'] =='no'){
        $sibling_in ='';
      }else{
        $sibling_in = (isset($input['sibling_in']) =='')? NULL : $input['sibling_in'];
       $sibling_detail = $this->settings->getSetting('school_name');
        if($sibling_in == 'other') {
          $sibling_detail = $input['sibling_studying'];
        }
      }
      $has_sibling = $input['has_sibling'];
      if($has_sibling == 'yes') $hasSibling = 1;
      else $hasSibling = 0;
    }

    $data = array(
      'academic_year' => (isset($input['academic_year']) =='')? NULL : $input['academic_year'], 
      'student_current_school' => (isset($input['student_current_school']) =='')? NULL : $input['student_current_school'],
      'student_name' => (isset($input['student_name']) =='')? NULL : $input['student_name'], 
      'student_last_name' => (isset($input['student_last_name']) =='')? NULL : $input['student_last_name'], 
      'interested_in' => (isset($input['interested_in']) =='')? NULL : $input['interested_in'], 
      'current_country' => (isset($input['current_country']) =='')? NULL : $input['current_country'], 
      'boarding_type' => (isset($input['boarding_type']) =='')? NULL : $input['boarding_type'], 
      'current_city' => (isset($input['current_city']) =='')? NULL : $input['current_city'], 
      'gender' => (isset($input['gender']) =='')? NULL : $input['gender'], 
      'student_dob' => (isset($input['student_dob']) =='')? NULL : date('Y-m-d',strtotime($input['student_dob'])), 
      'class_name' => (isset($input['class_name']) =='')? NULL : $input['class_name'], 
      'board_opted' => (isset($input['board_opted']) =='')? NULL : $input['board_opted'], 
      'parent_name' => (isset($input['parent_name']) =='')? NULL : $input['parent_name'],
      'enquiry_additional_coaching' => (isset($input['enquiry_additional_coaching']) =='')? NULL : $input['enquiry_additional_coaching'], 
      'enquiry_combination' => (isset($input['enquiry_combination']) =='')? NULL : $input['enquiry_combination'],
      'residential_address' => (isset($input['residential_address']) =='')? NULL : $input['residential_address'],
      'parent_occupation' => (isset($input['parent_occupation']) =='')? NULL : $input['parent_occupation'],
      'additional_education_needs' => $additional_education_needs_details,
      'previous_academic_report' => (isset($input['previous_academic_report']) =='')? NULL : $input['previous_academic_report'],
      'is_transfer_certificate_available' => (isset($input['is_transfer_certificate_available']) =='')? NULL : $input['is_transfer_certificate_available'],
      'mobile_number' =>(isset($input['mobile_number']) =='')? NULL : $input['mobile_number'],
      'alternate_mobile_number' => (isset($input['alternate_mobile_number']) =='')? NULL : $input['alternate_mobile_number'], 
      'email' => (isset($input['email']) =='')? NULL : $input['email'],
      'message' => (isset($input['message']) =='')? NULL : $input['message'],
      'status' => 'Created', 
      'created_by' => (isset($input['created_by']) =='')? NULL : $input['created_by'], 
      'board' => (isset($input['board']) =='')? NULL : $input['board'],
      'medium_of_instruction' => (isset($input['medium_of_instruction']) =='')? NULL : $input['medium_of_instruction'],
      'got_to_know_by' => $known_by,
      'source' => (isset($input['source']) =='')? NULL : $input['source'],
      'assigned_to' => (isset($input['assigned_to']) =='')? NULL : $input['assigned_to'],
      'has_sibling' => (isset($input['has_sibling']))?$hasSibling:0,
      'where_is_sibling' => $sibling_detail,
      'father_name' => (isset($input['father_name']) =='')? NULL : $input['father_name'],
      'mother_name' => (isset($input['mother_name']) =='')? NULL : $input['mother_name'],
      'student_phone_number' => (isset($input['student_phone_number']) =='')? NULL : $input['student_phone_number'],
      'father_phone_number' => (isset($input['father_phone_number']) =='')? NULL : $input['father_phone_number'],
      'mother_phone_number' => (isset($input['mother_phone_number']) =='')? NULL : $input['mother_phone_number'],
      'student_email_id' => (isset($input['student_email_id']) =='')? NULL : $input['student_email_id'],
      'mother_email_id' => (isset($input['mother_email_id']) =='')? NULL : $input['mother_email_id'],
      'father_email_id' => (isset($input['father_email_id']) =='')? NULL : $input['father_email_id'],
      'mother_occupation' => (isset($input['mother_occupation']) =='')? NULL : $input['mother_occupation'],
      'sibling_class' => (isset($input['sibling_class']) =='')? NULL : $input['sibling_class'],
      'sibling_name' => (isset($input['sibling_name']) =='')? NULL : $input['sibling_name'],
      'transportation_facility' => (isset($input['transportation_facility']) =='')? NULL : $input['transportation_facility'],
      'father_occupation' => (isset($input['father_occupation']) =='')? NULL : $input['father_occupation'],
      'reason_for_leaving_school' => (isset($input['reason_for_leaving_school']) =='')? NULL : $input['reason_for_leaving_school'],
      'wtsapp_number' => (isset($input['wtsapp_number']) =='')? NULL : $input['wtsapp_number'],
      'university' => (isset($input['university']) =='')? NULL : $input['university'],
      'created_on'=>(isset($input['cated_date'])) ? date('Y-m-d',strtotime($input['cated_date'])) : date('Y-m-d H:i:s',strtotime(date('Y-m-d H:i:s'))),
      'is_registered_parent' =>(isset($input['is_registered']))? $input['is_registered'] : 0,
      'year_of_passing' =>(isset($input['year_of_passing']))? $input['year_of_passing'] : null,
      'total_marks' =>(isset($input['total_marks']))? $input['total_marks'] : null,
      'marks_in_percentage' =>(isset($input['marks_in_percentage']))? $input['marks_in_percentage'] : null,
      'examination_passed' =>(isset($input['examination_passed']))? $input['examination_passed'] : null,
      'category'=>(isset($input['category']))? $input['category'] : null,
      'caste'=>(isset($input['caste']))? $input['caste'] : null,
      'currently_studying'=>(isset($input['currently_studying']))? $input['currently_studying'] : null,
      'referred_by' => isset($input['referred_by'])? $input['referred_by'] : NULL,
    );
    // echo "<pre>"; print_r($data); die();
    $this->db->insert('enquiry',$data);
    return $this->db->insert_id();
  }

  public function get_added_closures(){
    return $this->db_readonly->query("select distinct closure_reason from follow_up where closure_reason is not null")->result();
  }

  public function get_enquiry_byId($id){
    $result =  $this->db_readonly->select('e.*, c.class_name')
    ->from('enquiry e')
    ->where('e.id',$id)
    ->join('class c','e.class_name=c.id','left')
    ->get()->row();

    $followup =  $this->db_readonly->select('efu.id,efu.follow_up_action, efu.next_follow_date, efu.status, sm.first_name, date_format(efu.created_on,"%d-%m-%Y %H:%i") as created_on, efu.remarks, efu.closure_reason')
    ->from('follow_up efu')
    ->where('efu.follow_up_type','Enquiry')
    ->where('efu.source_id',$id)
    ->join('avatar a','a.id=efu.created_by','left')
    ->join('staff_master sm','sm.id=a.stakeholder_id','left')
    ->order_by('efu.id','desc')
    ->get()->result();
    $result->followup_history = $followup;
    return $result;

  }

  public function update_enquiry_follow_up_data($id, $input){
    $this->db->trans_start();
    $data = array(
      'follow_up_type' => $input['follow_up_type'], 
      'follow_up_action' =>$input['followup_action'],
      'source_id' => $id,
      'registered_email' => (isset($input['registered_email']) =='')? NULL: $input['registered_email'],
      'email_subject' => (isset($input['email_subject']) == '')? NULL: $input['email_subject'],
      'email_ids' => (isset($input['to_mails']) =='')? NULL:$input['to_mails'],
      'template_name' =>  (isset($input['template_name']) =='')? NULL:$input['template_name'],
      'template_content' => (isset($input['template_content']) =='')? NULL:$input['template_content'],
      'status' =>  $input['status'], 
      'created_by' => $this->authorization->getAvatarId(), 
      'remarks' => ($input['message'] =='')? '':$input['message'],
      'delivery_status' => 'Delivered', // check if email or sms
      'next_follow_date' =>($input['next_follow_date'] =='')? NULL: date('Y-m-d',strtotime($input['next_follow_date'])),
      'created_on' => $this->Kolkata_datetime(),
      'closure_reason' => (!isset($input['closure_reason']) || $input['closure_reason'] == '')?NULL:$input['closure_reason'],
      'lead_status' => (isset($input['lead_status']) =='')? NULL:$input['lead_status']
    );
    $this->db->insert('follow_up',$data);

    $eData = array(
      'status'=>$input['status'],
      'next_follow_date'=>($input['next_follow_date'] =='')? NULL: date('Y-m-d',strtotime($input['next_follow_date'])),
      'lead_status' => (isset($input['lead_status']) =='')? NULL:$input['lead_status']);
    
    $this->db->where('id',$id);
    $this->db->update('enquiry',$eData);

    $this->db->trans_complete();

    return $this->db->trans_status();
  }

  public function delete_enquiry_follow_up_data($id){
    $this->db->where('id',$id);
    return $this->db->delete('enquiry_follow_up');
  }

  public function get_enquiry_summary_report($acadyar){
    $table ='class';
    $table1 ='class_master';
    if($this->settings->getSetting('custom_class_from_enquiry_class_table')){
      $table ='enquiry_class';
      $table1 ='enquiry_class';
    }

    $this->db_readonly->select("c.class_name, esm.reporting_status, e.status, e.academic_year, count(e.id) as count")
      ->from('enquiry e')
      ->join($table.' c','e.class_name=c.id and e.academic_year=c.acad_year_id','left')
      ->join('enquiry_internal_status_map esm', 'e.status=esm.user_status', 'left')
      ->group_by('c.id, e.status')
      ->order_by('c.display_order')
      ->where('e.academic_year',$acadyar);

    $result = $this->db_readonly->get()->result();


    //Get the Overall status
    $overall_status_list = [
      ['status' => 'wip'],
      ['status' => 'closed'],
      ['status' => 'convert'],
      ['status' => 'invalid'],
      ['status' => 'unassigned']
    ];

    //Get the status list
    $status_list = $this->db_readonly->select('*')
      ->from('enquiry_internal_status_map')
      ->order_by('process_order, id')
      ->get()->result();

    //Add unasssigned to the list
    $temp = new stdClass();
    $temp->user_status = 'Unassigned';
    $temp->reporting_status = 'unassigned';
    $temp->color_code = '#ff0000';
    $process_order = 9999;
    $status_list[] = $temp;

    //Get the list of class names
    $grade_names = $this->db_readonly->select('class_name')
      ->from($table1)
      ->get()->result();

    //Add a empty class
    $temp = new stdClass();
    $temp->class_name = '';
    $grade_names[] = $temp;

    $grade_enquiry_final_arr = array();
    foreach ($grade_names as $grade) {
      $temp = array();
      $temp['class_name'] = $grade->class_name;
      foreach ($status_list as $status) {
        $temp_status = $status->user_status;
        $temp[$temp_status] = 0;
      }
      foreach ($overall_status_list as $os) {
        $stat = $os['status'];
        $temp[$stat] = 0;
      }
      $grade_enquiry_final_arr[] =$temp;
    }
    
    foreach ($grade_enquiry_final_arr as &$grade_arr) {
      $unassigned = 0;
      foreach ($result as $res) {
        if ($res->class_name == $grade_arr['class_name']) {
          if (isset($grade_arr[$res->status]))
            $grade_arr[$res->status] = $res->count;
          else 
            $unassigned += $res->count;            
        }
      }
      $grade_arr['Unassigned'] = $unassigned;
    }

    foreach ($grade_enquiry_final_arr as &$grade_arr) {
      foreach ($result as $res1) {
        if ($res1->class_name == $grade_arr['class_name']) {
          if (isset($grade_arr[$res1->reporting_status]))
            $grade_arr[$res1->reporting_status] += $res1->count;
        }
      }
    }

    $data['overall_status_list'] = $overall_status_list;
    $data['status_list'] = $status_list;
    $data['grade_status_array'] = $grade_enquiry_final_arr;
    $data['overall_status_count'] = count($overall_status_list);
    $data['status_list_count'] = count($status_list);

    // echo '<pre>';print_r($data);die();

    return $data;
  }
  
  public function getClassByAcadYear($acad_year_id) {
     $table ='class';

    if($this->settings->getSetting('custom_class_from_enquiry_class_table')){
      $table ='enquiry_class';
    }
    $donot_show_classes = $this->settings->getSetting('custom_class_from_enquiry_class_table');
    $this->db_readonly->select('c.id,c.class_name');
    $this->db_readonly->from($table.' c');
    $this->db_readonly->where('acad_year_id',$acad_year_id);
    $this->db_readonly->where('c.is_placeholder!=1');
    $this->db_readonly->where('status', '1');
    $this->db_readonly->order_by('c.display_order, c.id');
   return $this->db_readonly->get()->result();
  }

  public function getClassByAcadYear_new($acad_year_id){
    $table ='class';

    if($this->settings->getSetting('custom_class_from_enquiry_class_table')){
      $table ='enquiry_class';
    }
    $donot_show_classes = $this->settings->getSetting('enquiry_donot_show_classes_inEnquiry');
    $this->db_readonly->select('c.id,c.class_name');
    $this->db_readonly->from($table.' c');
    $this->db_readonly->where('acad_year_id',$acad_year_id);
    $this->db_readonly->where('c.is_placeholder!=1');
    $this->db_readonly->where('status', '1');
    if($donot_show_classes){
      $this->db_readonly->where('donot_showin_enquiry !=', '1');
    }
    $this->db_readonly->order_by('c.display_order, c.id');
   return $this->db_readonly->get()->result();
  }

  public function getClassByAcadYear_generate($acad_year_id) {
    $table ='class';
    if($this->settings->getSetting('custom_class_from_enquiry_class_table')){
      $table ='enquiry_class';
    }
    
    $this->db_readonly->select('c.id,c.class_name');
    $this->db_readonly->from($table.' c');
    $this->db_readonly->where('acad_year_id',$acad_year_id);
    $this->db_readonly->where('c.is_placeholder!=1');
    $this->db_readonly->where('status', '1');
    $this->db_readonly->order_by('c.display_order, c.id');
    $this->db_readonly->group_by('c.id');
   return $this->db_readonly->get()->result();
  }

  public function get_validate_ClassByAcadYear($acad_year_id,$dob){
    // echo '<pre>';print_r($dob);die();
    $table ='class';
    if($this->settings->getSetting('custom_class_from_enquiry_class_table')){
      $table ='enquiry_class';
    }
    if($dob == ''){
      return array();
    }
    $dont_show = $this->settings->getSetting('enquiry_donot_show_classes_inEnquiry');
    $dob_n = date('Y-m-d',strtotime($dob));
      $this->db->select('c.id,c.class_name,donot_showin_enquiry,min_dob,max_dob');
      $this->db->from($table.' c');
      $this->db->where('acad_year_id',$acad_year_id);
      $this->db->where('c.is_placeholder!=1');
      $this->db->where('status', '1');
      if($dont_show == 1){
        $this->db->where('donot_showin_enquiry !=',1);
      }
      $this->db->where("(c.min_dob <= '$dob_n' AND c.max_dob >='$dob_n')");
      $this->db->order_by('c.display_order, c.id');
      $this->db->group_by('c.id');
      $result = $this->db->get()->result();
      return $result;
  }

  public function get_enquiry_email_templatebyId($enquiry_id, $emailtemplateId){        
    $email_template = $this->db_readonly->select('*')
    ->from('email_template')
    ->where('name',$emailtemplateId)
    ->get()->row();
    if (!empty($email_template)) {
      $toEmail = $this->db_readonly->select('e.id as enId,ifnull( e.email,"") as parent_email, e.student_name, c.class_name, e.enquiry_number, e.parent_name, date_format(e.created_on,"%d-%m-%Y") as created_date, date_format(e.created_on,"%h:%i %p") as created_time,ifnull(mother_email_id,"") as mother_email_id,ifnull(father_email_id,"") as father_email_id')
      ->from('enquiry e')
      ->join('class c','e.class_name=c.id','left')
      ->where('e.id',$enquiry_id)
      ->get()->row();
      $email_template->to_email = $toEmail;
      return $email_template;
    }else{
      return 0;
    }
    
  }

  public function get_enquiry_sms_templatebyId($enquiry_id, $smsTemplateId){
    $email_template = $this->db_readonly->select('stn.id, stn.name, stn.category, stay.content, stay.acad_year_id')
    ->from('sms_template_new stn')
    ->join('sms_templates_acad_year stay','stn.id=stay.sms_templates_id')
    ->where('name',$smsTemplateId)
    ->get()->row();
    if (!empty($email_template)) {
    $toEmail = $this->db_readonly->select('e.id as enId, e.email as parent_email, e.student_name, c.class_name as className')
    ->from('enquiry e')
    ->where('e.id',$enquiry_id)
    ->join('class c','e.class_name=c.id')
    ->get()->row();
    $email_template->to_email = $toEmail;    
    return $email_template;
    }else{
      return 0;
    }
  }

  public function get_enquiry_all_data($selectedIndex,$class_list,$ad_status){
    $yearId = $this->acad_year->getAcadYearID();
    $class_table = 'class';
    if($this->settings->getSetting('custom_class_from_enquiry_class_table') == 1){
      $class_table = 'enquiry_class';
    }
    $this->db_readonly->select("e.id, (case when created_by = 0 then 'Parent' else sm.first_name end) as created_by,   ".$selectedIndex)
    ->from('enquiry e')
    ->where('e.academic_year',$yearId)
    ->join($class_table.' c','c.id=e.class_name','left')
    ->join('avatar a','a.id=e.created_by','left')
    ->join('student_admission sa','sa.admission_no=e.referred_by','left')
    ->join('student_year sy','sy.student_admission_id=sa.id and sy.promotion_status="STUDYING"','left')
    ->join('class cn','sy.class_id=cn.id','left')
    ->join('class_section cs','sy.class_section_id=cs.id','left')
    ->join('staff_master sm','sm.id=a.stakeholder_id','left');
    if ($class_list) {
      $this->db_readonly->where('c.id',$class_list);
    }
    if ($ad_status) {
      $this->db_readonly->where('e.status',$ad_status);
    }
    $result =  $this->db_readonly->get()->result();  
    $followUp = $this->db_readonly->select("
    date_format(created_on,'%d-%b') as createdDate, 
    remarks, 
    closure_reason, 
    source_id, 
    COUNT(fu.source_id) as followup_count
    ")
    ->from('follow_up fu')
    ->where('follow_up_type', 'Enquiry')
    ->group_by('source_id') // Ensure count is per student
    ->get()
    ->result();
    foreach ($result as $key => $val) {
      $val->remarks = '';
      $val->follow_up_count = 0;
      foreach ($followUp as $key => $follow) {
        if ($val->id == $follow->source_id) {
          $val->remarks .= $follow->createdDate.' : '.$follow->remarks.'. '.' ';
        $val->latest = $follow->createdDate.' : '.$follow->remarks.'. '.' ';
        $val->closure_reason = $follow->closure_reason;
        $val->follow_up_count = $follow->followup_count;
        }
      }
    }
    return $result;
  }

  public function getEnquiryApplyType() {
    $yearId = $this->acad_year->getAcadYearID();
    $result = $this->db_readonly->query("select count(id) as apply_count, source from enquiry where academic_year = $yearId group by source")->result();
    $total = 0;
    $data = [];
    foreach ($result as $key => $val) {
      $total += $val->apply_count;
      $name = ucwords(str_replace("_", " ", $val->source));
      $data[$name] = $val->apply_count;
    }
    return $data;
  }

  public function getEnquiryOverWeek() {
    // $yearId = $this->acad_year->getAcadYearID();
    // return $this->db_readonly->query("select count(id) as enquiries, DATE_FORMAT(created_on,'%d-%b') as week, DATE_FORMAT(created_on,'%d-%b-%y') as weekYear from enquiry where academic_year = $yearId group by WEEK(created_on) order by created_on,'desc'")->result();

    $startDate = date('Y-m-d', strtotime('-7 week')); 
    $endDate = date('Y-m-d');

    $this->db_readonly->select("count(tm.id) as enquiries, date_format(tm.created_on,'%U') as week, DATE_FORMAT(tm.created_on,'%d-%b-%y') as weekYear")
    ->from('enquiry tm')
    ->group_by("WEEK(tm.created_on)")
    ->order_by('tm.created_on','desc');
    $this->db_readonly->where('date_format(tm.created_on,"%Y-%m-%d") BETWEEN "'.$startDate. '" and "'.$endDate.'"');
    $enquiryCount = $this->db_readonly->get()->result();
    $dateArry = array();         
   
    $startDate1 = date('d-m-Y', strtotime('-7 week')); 
    $endDate2 = date('d-m-Y');
    $Year = date('Y');
    $weeksget = [];
    while (strtotime($startDate1) <= strtotime($endDate2)) {
      $startDate1 = date('d-m-Y', strtotime('+7 day', strtotime($startDate1)));
      if (strtotime($startDate1) > strtotime($endDate2)) {
        $week = [$endDate2];
      }
      else {
        $week = [date('d-m-Y', strtotime('-1 day', strtotime($startDate1)))];
      }
      foreach ($week as $key => $w) {
        $weeksget[$w] = date('W',strtotime($w));
      }
    }
    $temp = [];
    foreach ($enquiryCount as $key => $count) {
      $temp[$count->week] = $count->enquiries;
    }
    $totalEnquiry = [];
    foreach ($weeksget as $key => $val) {
      $startEnddate = $this->_getStartAndEndDate($val, $Year);
      if (array_key_exists($val, $temp)) {
        $totalEnquiry[$startEnddate['week_start'].' - '.$startEnddate['week_end'] ] = $temp[$val];
      }else{
        $totalEnquiry[$startEnddate['week_start'].' - '.$startEnddate['week_end']] = 0;
      }
    }
    return $totalEnquiry;

  }
  public function _getStartAndEndDate($week, $year) {
    $dto = new DateTime();
    $dto->setISODate($year, $week);
    $ret['week_start'] = $dto->format('d M');
    $dto->modify('+7 days');
    $ret['week_end'] = $dto->format('d M');
    return $ret;
  }

  public function getActivityOverWeek() {
    // $yearId = $this->acad_year->getAcadYearID();
    // return $this->db_readonly->query("select count(fu.id) as followups, DATE_FORMAT(fu.created_on,'%d-%b') as week, DATE_FORMAT(fu.created_on,'%d-%b-%y') as weekYear from follow_up fu join enquiry e on fu.source_id=e.id where academic_year = $yearId and fu.follow_up_type='Enquiry'  group by WEEK(fu.created_on) order by fu.created_on,'desc'")->result();

    $startDate = date('Y-m-d', strtotime('-7 week')); 
    $endDate = date('Y-m-d');

    $this->db_readonly->select("count(tm.id) as enquiries, date_format(tm.created_on,'%U') as week, DATE_FORMAT(tm.created_on,'%d-%b-%y') as weekYear")
    ->from('enquiry tm')
    ->where('tm.status!=','Created')
    ->group_by("WEEK(tm.created_on)")
    ->order_by('tm.created_on','desc');
    $this->db_readonly->where('date_format(tm.created_on,"%Y-%m-%d") BETWEEN "'.$startDate. '" and "'.$endDate.'"');
    $enquiryAcitivtyCount = $this->db_readonly->get()->result();
    $dateArry = array();         
   
    $startDate1 = date('d-m-Y', strtotime('-7 week')); 
    $endDate2 = date('d-m-Y');
    $Year = date('Y');
    $weeksget = [];
    while (strtotime($startDate1) <= strtotime($endDate2)) {
      $startDate1 = date('d-m-Y', strtotime('+7 day', strtotime($startDate1)));
      if (strtotime($startDate1) > strtotime($endDate2)) {
        $week = [$endDate2];
      }
      else {
        $week = [date('d-m-Y', strtotime('-1 day', strtotime($startDate1)))];
      }
      foreach ($week as $key => $w) {
        $weeksget[$w] = date('W',strtotime($w));
      }
    }
    $temp = [];
    foreach ($enquiryAcitivtyCount as $key => $count) {
      $temp[$count->week] = $count->enquiries;
    }
    $totalActivityEnquiry = [];
    foreach ($weeksget as $key => $val) {
      $startEnddate = $this->_getStartAndEndDate($val, $Year);
      if (array_key_exists($val, $temp)) {
        $totalActivityEnquiry[$startEnddate['week_start'].' - '.$startEnddate['week_end'] ] = $temp[$val];
      }else{
        $totalActivityEnquiry[$startEnddate['week_start'].' - '.$startEnddate['week_end']] = 0;
      }
    }
    return $totalActivityEnquiry;

  }

  public function getEnquiryStatus() {
    $yearId = $this->acad_year->getAcadYearID();
    $result = $this->db_readonly->query("select count(id) as status_count, status from enquiry where academic_year =$yearId group by status")->result();
    $data = array(
      'Closed-not interested' => 0,
      'Created' => 0,
      'Follow-up required' => 0,
      'Processed for admission' => 0
    );
    foreach ($result as $key => $value) {
      $data[$value->status] = $value->status_count;
    }
    return $data;
  }

  public function getEnquirySource() {
    $yearId = $this->acad_year->getAcadYearID();
    return $this->db_readonly->query("select count(id) as source_count, got_to_know_by as source from enquiry where academic_year=$yearId
    group by got_to_know_by having source != '' and source is not null
    order by source_count desc limit 10")->result();
  }

  public function getEnquiryStaffWise() {
    $yearId = $this->acad_year->getAcadYearID();
    $status_arr = json_decode($this->settings->getSetting('enquiry_follow_up_status'));
    if(empty($status_arr)) {
      return array();
    }

    $sql = "select e.id as enquiry_id,  concat(ifnull(sm.first_name,''),' ', ifnull(sm.last_name, '')) as staffName,sm.id as staff_id, e.status 
            from enquiry e 
            join staff_master sm on sm.id=e.assigned_to 
            where e.assigned_to!=0 and e.assigned_to is not null and academic_year =$yearId
            order by sm.first_name
            ";
    $result = $this->db_readonly->query($sql)->result();

    $finalStatus = array();
    foreach ($result as $key => $val) {
      if(!array_key_exists($val->enquiry_id, $finalStatus)) {
        $finalStatus[$val->enquiry_id] = new stdClass();
      }
      $finalStatus[$val->enquiry_id] = $val;
    }
    
    $handled = array();
    foreach ($finalStatus as $key => $val) {
      $staffId = $val->staff_id;
      if(!array_key_exists($staffId, $handled)) {
        $handled[$staffId] = array();
        $handled[$staffId]['name'] = $val->staffName;
        $handled[$staffId]['Assigned'] = 0;
        foreach ($status_arr as $status) {
          $handled[$staffId][$status] = 0;
        }
      }
      $handled[$staffId]['Assigned']++;
      if(array_key_exists($val->status, $handled[$staffId]))
        $handled[$staffId][$val->status]++;
    }

    return $handled;
  }

  public function getTotalEnquiry() {
    $yearId = $this->acad_year->getAcadYearID();
    return $this->db_readonly->query("select count(id) as total, sum(case when created_by is null then 1 when created_by = 0 then 1 else 0 end) as online, sum(case when created_by is null then 0 when created_by = 0 then 0 else 1 end) as offline from enquiry where academic_year =$yearId")->row();
  }

  public function getEnquirySchoolWise() {
    $yearId = $this->acad_year->getAcadYearID();
    return $this->db_readonly->query("select count(id) as school_count, student_current_school as school from enquiry where student_current_school!='' and academic_year =$yearId group by student_current_school")->result();
  }

  public function getClosureReasons() {
    $yearId = $this->acad_year->getAcadYearID();
    return $this->db_readonly->query("select count(fu.id) as res_count, fu.closure_reason as reason from follow_up fu join enquiry e on fu.source_id=e.id where academic_year =$yearId and fu.follow_up_type='Enquiry' and fu.status='Closed-not interested' group by fu.closure_reason")->result();
  }

  public function getReportingStatus(){
    $yearId = $this->acad_year->getAcadYearID();
        $enquiry =  $this->db->select("status")
        ->from('enquiry')
        ->where('academic_year ='.$yearId)
        ->get()->result();

        $status_arr = $this->db->select("user_status,reporting_status,color_code")
        ->from('enquiry_internal_status_map')
        ->get()->result();

        $handled = array();
        foreach ($status_arr as $key => $status) {
            $handled[$status->reporting_status]['color_code'] = $status->color_code;
            $handled[$status->reporting_status]['count'] = 0;
        }

        foreach ($enquiry as $key => $val) {
            foreach ($status_arr as $k => $status) {
                if($val->status == $status->user_status){
                    $handled[$status->reporting_status]['count'] ++;
                }
            }
        }
        return $handled;
    }

  public function insert_enquiry_configure_fields(){
    $input = $this->input->post();
    $types = array(
      'enquiry_required_fields' => isset($input['enquiry_required_fields'])?$input['enquiry_required_fields']:[],
      'enquiry_show_disabled_fields' => isset($input['enquiry_show_disabled_fields'])?$input['enquiry_show_disabled_fields']:[],
    );
    foreach ($types as $name => $value) {
        $query = $this->db->where('name',$name)->get('config');
        if ($query->num_rows() > 0) {
          if(empty($value)) {
            $row = $query->row();
            $this->db->where('id', $row->id)->delete('config');
          } else {
            $UpdaterFields = array(
                'name' =>$name,
                'value' => json_encode($value),
                'type' => 'multiple'
            );
            $this->db->where('name',$name);
            $this->db->update('config',$UpdaterFields);
          }
        }else{
            $rFields = array(
                'name' =>$name,
                'value' => json_encode($value),
                'type' => 'multiple'
            );
            $this->db->insert('config',$rFields);
        }
        
    }
    return 1;
  }

  public function get_enquiry_required_fields(){
      $result = $this->db_readonly->select('*')
      ->from('config')
      ->where('name','enquiry_required_fields')
      ->get()->row();
      if (!empty($result)) {
         return json_decode($result->value);
      }else{
          return array();
      }
      
  }

  public function get_enquiry_disabled_fields(){
      $result = $this->db_readonly->select('*')
      ->from('config')
      ->where('name','enquiry_show_disabled_fields')
      ->get()->row();
      if (!empty($result)) {
         return json_decode($result->value);
      }else{
          return array();
      }
  }

  public function saveFieldValue() {
    $input = $this->input->post();
    return $this->db->where('id', $input['enquiry_id'])->update('enquiry', array($input['field_name'] => $input['value']));
  }

  public function updateEnquiryData($enquiry_id, $name, $value) {
    return $this->db->where('id', $enquiry_id)->update('enquiry', array($name => $value));
  }

  public function updateEnquiryData_staff($enquiryId, $staffId) {
    return $this->db->where('id', $enquiryId)->update('enquiry', array('assigned_to'=> $staffId));
  }

  public function getAcadYearBudget($acad_year_id) {
    $sql = "select count(e.id) as admissions, c.enquiry_budget, c.class_name, c.id as class_id 
            from enquiry e 
            left join class c on c.id=e.class_name 
            where e.academic_year=$acad_year_id 
            and status='Processed for admission' 
            group by e.class_name 
            order by c.class_name";
    return $this->db_readonly->query($sql)->result();
  }

  public function saveClassBudget($class_id, $budget) {
    return $this->db->where('id', $class_id)->update('class', array('enquiry_budget' => $budget));
  }

  public function get_enquiry_v2_byId($id){
    $table ='class';
    if($this->settings->getSetting('custom_class_from_enquiry_class_table')){
      $table ='enquiry_class';
    }

    $result =  $this->db_readonly->select('e.*, date_format(e.created_on,"%d-%m-%Y") as createdEnquiry, date_format(e.student_dob,"%d-%m-%Y") as std_dob,  c.class_name, (case when e.gender = "M" then "Male" when e.gender = "F" then "Female" else "Others" end) as s_gender, eism.reporting_status,ifnull(utm_campaign,"-") as utm_campaign,ifnull(utm_source,"-") as utm_source,ifnull(utm_medium,"-") as utm_medium,ifnull(utm_content,"-") as utm_content,ifnull(utm_term,"-") as utm_term,concat(sa.first_name,ifnull(sa.last_name,""))as referred_by_student,concat(cn.class_name,ifnull(cs.section_name,"")) as referred_by_class_setion,e.referred_by')
    ->from('enquiry e')
    ->where('e.id',$id)
    ->join($table.' c','e.class_name=c.id','left')
    ->join('enquiry_internal_status_map eism', 'eism.user_status=e.status', 'left')
    ->join('student_admission sa','e.referred_by=sa.admission_no','left')
    ->join('student_year sy','sy.student_admission_id=sa.id and sy.acad_year_id='.$this->acad_year->getAcadYearID(),'left')
    ->join('class cn','sy.class_id=cn.id','left')
    ->join('class_section cs','sy.class_section_id=cs.id','left')
    ->get()->row();
    $result->boardingType ='';
    if (!empty($result->boarding_type)) {
      $result->boardingType = $this->settings->getSetting('boarding')[$result->boarding_type];  
    }

    if (!empty($result->category)) {
      $result->category = $this->settings->getSetting('category')[$result->category];  
    }

    $followup =  $this->db_readonly->select('efu.id,efu.follow_up_action, ifnull(date_format(efu.next_follow_date,"%d-%m-%Y")," ") as next_follow_date, efu.status, ifnull(sm.first_name," ") as first_name, date_format(efu.created_on,"%d-%m-%Y %H:%i") as created_on, efu.remarks, efu.closure_reason,efu.lead_status')
    ->from('follow_up efu')
    ->where('efu.follow_up_type','Enquiry')
    ->where('efu.source_id',$id)
    ->join('avatar a','a.id=efu.created_by','left')
    ->join('staff_master sm','sm.id=a.stakeholder_id','left')
    ->order_by('efu.id','desc')
    ->get()->result();
    $result->followup_history = $followup;
    return $result;

  }

  public function get_all_lead_status(){
    return $this->db_readonly->select('distinct(e.lead_status)')->where('e.lead_status!=','NULL')->from('enquiry e')->get()->result();
  }

  public function get_enquiry_activities_details($from_date, $to_date, $counselor){
    $table ='class';
    if($this->settings->getSetting('custom_class_from_enquiry_class_table')){
      $table ='enquiry_class';
    }
    
    $fromDate = date('Y-m-d',strtotime($from_date));
    $toDate =date('Y-m-d',strtotime($to_date));
    $this->db_readonly->select('efu.id,efu.follow_up_action, efu.next_follow_date, efu.status, sm.first_name, date_format(efu.created_on,"%d-%m-%Y") as created_on, UNIX_TIMESTAMP(efu.created_on) AS followupCreatedDate, efu.remarks, efu.closure_reason, source_id,efu.email_sent_to_ids');
    $this->db_readonly->from('follow_up efu');
    $this->db_readonly->join('enquiry e','efu.source_id=e.id');
    if ($fromDate && $toDate) {
      $this->db_readonly->where('date_format(efu.created_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
    }
    $this->db_readonly->where('efu.follow_up_type','Enquiry');
    $this->db_readonly->where('e.academic_year',$this->acad_year->getAcadYearID());
   
    $this->db_readonly->join('avatar a','a.id=efu.created_by','left');
    $this->db_readonly->join('staff_master sm','sm.id=a.stakeholder_id','left');
    if ($counselor) {
      $this->db_readonly->where_in('sm.id',$counselor);
    }

    $this->db_readonly->order_by('efu.created_on','asc');
    $followup = $this->db_readonly->get()->result();
    
    $dateArry = [];
    foreach ($followup as $key => $value) {
      $value->email_sent_to_ids = explode(',',$value->email_sent_to_ids);
      $dateArry[$value->created_on][] = $value;

      if(!empty($value->email_sent_to_ids) && $value->follow_up_action == 'Email'){
        $value->email_status = '';
        $email_status = ''; 

        if(!empty($value->email_sent_to_ids[0])){
            $email_status = 'Student Email - ' . $this->get_email_status($value->email_sent_to_ids[0]);
        }

        if(!empty($value->email_sent_to_ids[1])){
            $email_status .= (!empty($email_status) ? ', ' : '') . 'Father Email - ' . $this->get_email_status($value->email_sent_to_ids[1]);
        }

        if(!empty($value->email_sent_to_ids[2])){
            $email_status .= (!empty($email_status) ? ', ' : '') . 'Mother Email - ' . $this->get_email_status($value->email_sent_to_ids[2]);
        }

        $value->email_status = $email_status;

        }
    }
    $countArry = [];
    foreach ($dateArry as $date => $val) {
      $countArry[$date] = count($val);
    }
    $sortArry = [];
    $sourceIds = [];
    foreach($followup as $val){
      if(!in_array($val->source_id, $sourceIds, true)){
        array_push($sourceIds, $val->source_id);
      }
      $sourceIdsArr[$val->source_id][] = $val;
      $sortArry[$val->source_id] = $val;
    }
    if (empty($sourceIds)) {
      return array();
    }
    $enquiry =  $this->db_readonly->select("e.*, ifnull(c.class_name,'') as class_name")
    ->from('enquiry e')
    ->where_in('e.id',$sourceIds)
    ->join($table.' c', 'e.class_name=c.id','left')
    ->get()->result();

    foreach ($enquiry as $key => &$val) {
      if (array_key_exists($val->id, $sourceIdsArr)) {
        $val->sortbyDate = $sortArry[$val->id]->id;
      }
    }
    $followupCreatedDate = array_column($enquiry, 'sortbyDate');
    array_multisort($followupCreatedDate, SORT_DESC, $enquiry);
    return array('enquiry'=>$enquiry, 'followup'=>$sourceIdsArr, 'linechart'=>$countArry);
  }

  public function get_email_status($email_sent_id){
    return $this->db->select('status')->from('email_sent_to')->where('id',$email_sent_id)->get()->row()->status;
  }

  public function get_enquiry_created_follow_date($createdfrom_date,$createdto_date,$followupfrom_date,$followupto_date,$counselor, $follow_up_status){
    $table ='class';
    if($this->settings->getSetting('custom_class_from_enquiry_class_table')){
      $table ='enquiry_class';
    }

    $yearId = $this->acad_year->getAcadYearID();
    $this->db_readonly->select("e.id, ifnull(e.parent_name,'') as parent_name, ifnull(e.student_name,'') as student_name, ifnull(e.mobile_number,'') as mobile_number, ifnull(date_format(e.next_follow_date,'%d-%m-%Y'),'') as next_follow_date, ifnull(e.email,'') as email, ifnull(e.source,'') as source, ifnull(e.assigned_to,'') as assigned_to, date_format(e.created_on,'%d-%m-%Y') as createdDate, ifnull(c.class_name,'') as class_name, ifnull(sm1.first_name,'') as counselor, e.status,e.academic_year, eis.color_code as background_colorCode");
    $this->db_readonly->from('enquiry e');
    $this->db_readonly->where('e.academic_year', $yearId);
    $this->db_readonly->join('enquiry_internal_status_map eis', 'eis.user_status=e.status' );
    $this->db_readonly->where('eis.reporting_status !=', 'invalid');

    //echo "<pre>"; print_r($sdata); die();
    if ($counselor) {
      if ($counselor[0] == -1) {

        $this->db_readonly->group_start();
        $this->db_readonly->where('e.assigned_to', 0);
        $this->db_readonly->or_where('e.assigned_to', null);
        $this->db_readonly->or_where_in('e.assigned_to', $counselor);
        $this->db_readonly->group_end();
      } else{
        $this->db_readonly->where_in('e.assigned_to',$counselor);
      }
    }
    if ($createdfrom_date && $createdto_date) {
      $createdfrom_date = date('Y-m-d',strtotime($createdfrom_date));
      $createdto_date = date('Y-m-d',strtotime($createdto_date));
      $this->db_readonly->where('date_format(e.created_on,"%Y-%m-%d") BETWEEN "'.$createdfrom_date. '" and "'.$createdto_date.'"');
    }

    if ($followupfrom_date && $followupto_date) {
      $followupfrom_date = date('Y-m-d',strtotime($followupfrom_date));
      $followupto_date = date('Y-m-d',strtotime($followupto_date));
      $this->db_readonly->where('date_format(e.next_follow_date,"%Y-%m-%d") BETWEEN "'.$followupfrom_date. '" and "'.$followupto_date.'"');
    }
    
    if ($follow_up_status) {
     $this->db_readonly->where_in('e.status',$follow_up_status);
    }
    $this->db_readonly->join($table.' c','e.class_name=c.id','left');
    $this->db_readonly->join('staff_master sm1','sm1.id=e.assigned_to','left');
    $this->db_readonly->order_by('e.id','desc');
    return $this->db_readonly->get()->result();
    // echo "<pre>"; print_r($this->db_readonly->last_query()); die();
  }

  public function update_counselor_by_selected_enquiries($counselor_id, $enquiry_ids){
    $enArry = [];
    foreach ($enquiry_ids as $key => $enId) {
      $enArry[] = array('id'=>$enId,'assigned_to'=>$counselor_id);
    }
    return $this->db->update_batch('enquiry', $enArry, 'id');
  }

  public function email_template_enquiry_staff_id_update($email_template_staff, $staff_id){
    $query = $this->db->get('enquiry_settings');
    if ($query->num_rows() > 0) {
      $this->db->where('id',$query->row()->id);
      $this->db->update('enquiry_settings',array('email_template_id_staff'=>$email_template_staff, 'staff_id'=> json_encode($staff_id)));
      return $this->db->affected_rows();
    }else{
      return $this->db->insert('enquiry_settings',array('email_template_id_staff'=>$email_template_staff, 'staff_id'=> json_encode($staff_id)));
    }
    
  }

  // public function email_template_enquiry_update($email_template_staff, $staff_id){
  //   $query = $this->db->get_where('enquiry_settings', array('staff_id' => json_encode($staff_id)));
  //   if ($query->num_rows() > 0) {
  //     $row= $query->row();
  //     $this->db->where('id',$query->row()->id);
  //     $this->db->update('enquiry_settings',array('email_template_id_staff'=>$email_template_staff, 'staff_id'=> json_encode($staff_id)));
  //     return $this->db->affected_rows();
  //   }else{
  //     return $this->db->insert('enquiry_settings',array('email_template_id_staff'=>$email_template_staff, 'staff_id'=> json_encode($staff_id)));
  //   }
    
  // }


  public function get_enquiry_email_assinged_details(){
    return  $this->db_readonly->select('es.staff_id, es.id, et.name')
    ->from('enquiry_settings es')
    ->join('email_template et','es.email_template_id_staff=et.id')
    ->get()->row();
  }

  public function get_enquiry_receipt_books(){
    return $this->db_readonly->get('feev2_receipt_book')->result();
  }

  public function update_receipt_book_in_enquiry(){
    $query = $this->db->get('enquiry_settings');
    if ($query->num_rows() > 0) {
      $this->db->where('id',$query->row()->id);
      $this->db->update('enquiry_settings',array('receipt_book_id'=>$this->input->post('receipt_book')));
      return $this->db->affected_rows();
    }else{
      return $this->db->insert('enquiry_settings',array('receipt_book_id'=>$this->input->post('receipt_book')));
    }
    
  }

  public function enquiry_settings_deletebyId($id){
    $this->db->where('id',$id);
    return $this->db->delete('enquiry_settings');
  }
  public function get_enquiry_email_staff_template_byId(){

    $result = $this->db->select('es.staff_id, et.email_subject, et.registered_email, et.content, et.members_email')
    ->from('enquiry_settings es')
    ->join('email_template et','es.email_template_id_staff=et.id')
    ->get()->row();
    if (!empty($result)) {
      $staffIds = json_decode($result->staff_id);

      $this->db->select('u.email as email, sm.id as staff_id, a.avatar_type');
      $this->db->from('staff_master sm');
      $this->db->where_in('sm.id',$staffIds);
      $this->db->join('avatar a', 'sm.id=a.stakeholder_id');
      $this->db->join('users u', 'a.user_id=u.id');
      $this->db->where('a.avatar_type','4'); //4: Staff
      $staff =  $this->db->get()->result();

      $result->staffdetails = $staff;
    }
    return $result;

  }

  public function insert_enquiry_from_email($input){
    $data = array(
      'academic_year' => '21', 
      'student_current_school' => '',
      'student_name' => $input["Name of your child"],
      'gender' => '',
      'student_dob' => date('Y-m-d',strtotime($input["Your child's birthday"])),
      'class_name' => $input["I am interested in the below grades for my child/children"],
      'parent_name' => $input["Parent's Name"],
      'mobile_number' => $input["Phone"],
      'alternate_mobile_number' => '',
      'email' => $input["Email"],
      'message' => '',
      'status' => 'Created', 
      'created_by' => '',
      'board' =>'',
      'got_to_know_by' => $input["How did you hear about Basil Woods?"],
      'source' => '',
      'assigned_to' => '',
      'has_sibling' => '',
      'where_is_sibling' => '',
      'created_on'=>date('Y-m-d H:i:s',strtotime(date('Y-m-d H:i:s')))
    );
   return  $this->db->insert('enquiry',$data);
  }


  public function insert_enquiry_data_api($input){
    
    $data = array(
      'academic_year' => (!isset($input['academic_year']) || $input['academic_year'] == '') ? '' : $input['academic_year'], 

      'student_current_school' => (!isset($input['student_current_school']) || $input['student_current_school'] == '') ? '' :$input['student_current_school'],

      'student_name' => (!isset($input['student_name']) || $input['student_name'] == '') ? '' : $input['student_name'], 

      'student_last_name' => (!isset($input['student_last_name']) || $input['student_last_name'] == '') ? '' : $input['student_last_name'], 

      'enquiry_additional_coaching' => (!isset($input['enquiry_additional_coaching']) || $input['enquiry_additional_coaching'] == '') ? '' : $input['enquiry_additional_coaching'], 

      'enquiry_combination' => (!isset($input['enquiry_combination']) || $input['enquiry_combination'] == '') ? '' : $input['enquiry_combination'],
      
      'residential_address' => (!isset($input['residential_address']) || $input['residential_address'] == '') ? '' : $input['residential_address'],

      'currently_studying' => (!isset($input['currently_studying']) || $input['currently_studying'] == '') ? '' : $input['currently_studying'],

      'gender' => (!isset($input['gender']) || $input['gender'] == '') ? 'M' : $input['gender'], 

      'student_dob' => (!isset($input['student_dob']) || $input['student_dob'] == '') ? '' : date('Y-m-d',strtotime($input['student_dob'])), 

      'class_name' => (!isset($input['class_id']) || $input['class_id'] == '') ? '' : $input['class_id'], 

      'parent_occupation' => (!isset($input['parent_occupation']) || $input['parent_occupation'] == '') ? '' : $input['parent_occupation'],

      'additional_education_needs' => (!isset($input['additional_education_needs']) || $input['additional_education_needs'] == '') ? '' : $input['additional_education_needs'],

      'previous_academic_report' => (!isset($input['previous_academic_report']) || $input['previous_academic_report'] == '') ? '' : $input['previous_academic_report'],

      'is_transfer_certificate_available' => (!isset($input['is_transfer_certificate_available']) || $input['is_transfer_certificate_available'] == '') ? '' : $input['is_transfer_certificate_available'],

      'parent_name' => (!isset($input['parent_name']) || $input['parent_name'] == '') ? '' : $input['parent_name'],

      'mobile_number' => (!isset($input['mobile_number']) || $input['mobile_number'] == '') ? '' : $input['mobile_number'],

      'alternate_mobile_number' => (!isset($input['alternate_mobile_number']) || $input['alternate_mobile_number'] == '') ? '' : $input['alternate_mobile_number'], 

      'email' => (!isset($input['email']) || $input['email'] == '') ? '' : $input['email'],

      'message' => (!isset($input['message']) || $input['message'] == '') ? '' : $input['message'],
      
      'status' => 'Created', 

      'created_by' => (!isset($input['created_by']) || $input['created_by'] == '') ? NULL : $input['created_by'], 
      
      'board' => (!isset($input['board']) || $input['board'] == '') ? '' : $input['board'],

      'medium_of_instruction' => (!isset($input['medium_of_instruction']) || $input['medium_of_instruction'] == '') ? '' : $input['medium_of_instruction'],

      'got_to_know_by' => (!isset($input['got_to_know_by']) || $input['got_to_know_by'] == '') ? '' : $input['got_to_know_by'],

      'interested_in' => (!isset($input['interested_in']) || $input['interested_in'] == '') ? '' : $input['interested_in'],

      'current_country' => (!isset($input['current_country']) || $input['current_country'] == '') ? '' : $input['current_country'],

      'current_city' => (!isset($input['current_city']) || $input['current_city'] == '') ? '' : $input['current_city'],

      'boarding_type' => (!isset($input['boarding_type']) || $input['boarding_type'] == '') ? '' : $input['boarding_type'], 

      'source' => (!isset($input['source']) || $input['source'] == '') ? '' : $input['source'],

      'assigned_to' => (!isset($input['assigned_to']) || $input['assigned_to'] == '') ? '' : $input['assigned_to'],

      'board_opted' => (!isset($input['board_opted']) || $input['board_opted'] == '') ? '' : $input['board_opted'],

      'page_url' => (!isset($input['page_url']) || $input['page_url'] == '') ? '' : $input['page_url'],

      'created_on'=> date('Y-m-d H:i:s',strtotime(date('Y-m-d H:i:s'))),

      'utm_campaign' => (!isset($input['utm_campaign']) || $input['utm_campaign'] == '') ? '' : $input['utm_campaign'],
      
      'utm_medium' => (!isset($input['utm_medium']) || $input['utm_medium'] == '') ? '' : $input['utm_medium'],

      'utm_source' => (!isset($input['utm_source']) || $input['utm_source'] == '') ? '' : $input['utm_source'],

      'utm_content' => (!isset($input['utm_content']) || $input['utm_content'] == '') ? '' : $input['utm_content'],

      'utm_term' => (!isset($input['utm_term']) || $input['utm_term'] == '') ? '' : $input['utm_term'],

      'father_name' => (!isset($input['father_name']) || $input['father_name'] == '') ? '' : $input['father_name'],

      'mother_name' => (!isset($input['mother_name']) || $input['mother_name'] == '') ? '' : $input['mother_name'],

      'father_phone_number' => (!isset($input['father_phone_number']) || $input['father_phone_number'] == '') ? '' : $input['father_phone_number'],

      'mother_phone_number' => (!isset($input['mother_phone_number']) || $input['mother_phone_number'] == '') ? '' : $input['mother_phone_number'],

      'mother_email_id' => (!isset($input['mother_email_id']) || $input['mother_email_id'] == '') ? '' : $input['mother_email_id'],

      'father_email_id' => (!isset($input['father_email_id']) || $input['father_email_id'] == '') ? '' : $input['father_email_id'],

      'father_occupation' => (!isset($input['father_occupation']) || $input['father_occupation'] == '') ? '' : $input['father_occupation'],

      'mother_occupation' => (!isset($input['mother_occupation']) || $input['mother_occupation'] == '') ? '' : $input['mother_occupation'],

      'utm_adgroup' => (!isset($input['utm_adgroup']) || $input['utm_adgroup'] == '') ? '' : $input['utm_adgroup'],

      'sibling_name' => (!isset($input['sibling_name']) || $input['sibling_name'] == '') ? '' : $input['sibling_name'],
    );
   $result = $this->db->insert('enquiry',$data);
   return $this->db->insert_id();
    //  if ($result) 
    //     return 1;
    //   else 
    //     return 0;   
  }

  public function validate_class_id($class_id, $acad_year_id){
    $query = $this->db_readonly->query('select * from class where id ='.$class_id.' and acad_year_id = '.$acad_year_id.'');  
    if ($query->num_rows() >= 1) {
      return 1;
    }else {
      return 0;
    }
  }

  public function update_receipt_enquiry_wise($enquiryId){
    $this->db->trans_start();
    $sql = "select frb.* from enquiry_settings es left join feev2_receipt_book frb on es.receipt_book_id=frb.id for update";
    $receipt_book = $this->db->query($sql)->row();
    if (!empty($receipt_book)) {
      $this->db->where('id',$receipt_book->id);
      $this->db->update('feev2_receipt_book', array('running_number'=>$receipt_book->running_number+1));
      
      $this->db->trans_complete();

      $receipt_number = $this->fee_library->receipt_format_get_update($receipt_book);
      $this->db->where('id',$enquiryId);
      return $this->db->update('enquiry', array('enquiry_number'=>$receipt_number));
    }else{
      return true;
    }
  }

  public function get_enquiry_number_by_id($enquiry_id){
    $enquiry_number = $this->db->select('enquiry_number')->from('enquiry')->where('id',$enquiry_id)->get()->row();
    if(empty($enquiry_number)){
      return '';
    }else{
      return $enquiry_number->enquiry_number;
    }
  }

  public function get_enquiry_parent_name_id($enquiry_id){
    return $this->db->where('id',$enquiry_id)->get('enquiry')->row()->parent_name;
  }

  public function get_enquiry_full_data($enquiry_id){
    $result=$this->db->select('c.class_name, parent_name,student_name,enquiry_number')->from('enquiry e')->join('class c', 'e.class_name=c.id')->where('e.id',$enquiry_id)->get()->row();
    return $result;
  }

  public function getcouncellor_name($assignedId){
    $result = $this->db_readonly->select("concat(ifnull(first_name,''),' ',ifnull(last_name,'')) as staff_name")
    ->from('staff_master')
    ->where('id',$assignedId)
    ->get()->row();
    if (!empty($result)) {
      return $result->staff_name;
    }else{
      return '';
    }
  }

   public function insert_enquiry_data_contactus_form(){
    $input = $this->input->post();

   
    $data = array(
      'academic_year' => (isset($input['academic_year']) =='')? NULL : $input['academic_year'], 
      'student_name' => (isset($input['student_name']) =='')? NULL : $input['student_name'], 
      'student_last_name' => (isset($input['student_last_name']) =='')? NULL : $input['student_last_name'], 
      'class_name' => (isset($input['class_name']) =='')? NULL : $input['class_name'], 
      'mobile_number' => $input['mobile_number'], 
      'email' => (isset($input['email']) =='')? NULL : $input['email'],
      'message' => (isset($input['message']) =='')? NULL : $input['message'],
      'status' => 'Created', 
      'created_on'=>(isset($input['cated_date'])) ? date('Y-m-d',strtotime($input['cated_date'])) : date('Y-m-d H:i:s',strtotime(date('Y-m-d H:i:s'))),
      'created_by' => (isset($input['created_by']) =='')? NULL : $input['created_by'], 
      'gender' => (isset($input['gender']) =='')? NULL : $input['gender'], 
      'parent_name' => (isset($input['parent_name']) =='')? 'Not Provided' : $input['parent_name'],
      'current_city' => (isset($input['current_city']) =='')? null : $input['current_city'],
      'source' => (isset($input['source']) =='')? null : $input['source']
    );

    $this->db->insert('enquiry',$data);
    return $this->db->insert_id();
  }

  public function get_enquiry_status_data() {
    $result = $this->db_readonly->select("*")
    ->from("enquiry_internal_status_map")
    ->get()->result();
  return $result;
  }

  public function submit_enquiry_status($input) {
    $data=array(
        'user_status' =>$input['user_status'],
        'reporting_status'=> $input['reporting_status'],
        'color_code' => $input['color_code']
      );
    return  $this->db->insert('enquiry_internal_status_map',$data);
  }

  public function update_enquiry_status($input) {
    $data=array(
        'user_status' =>$input['edit_user_status'],
        'reporting_status'=> $input['edit_reporting_status'],
        'color_code' => $input['edit_color_code']
      );
      $this->db->where('id',$input['status_id']);
    return  $this->db->update('enquiry_internal_status_map',$data);
  }

 public function get_email_template_for_communication(){
   $result = $this->db_readonly->select('et.name')
    ->from('email_template et')
    ->get()->result();
    $etArry = [];
    foreach ($result as $key => $value) {
      array_push($etArry, $value->name);
    }
    return $etArry;
  }

  public function insertOTP($input, $otp) {
    $this->db->where('mobile_number', $input['mobileNumber']);
    $q = $this->db->get('enquiry_user');
    $this->db->reset_query();
    if ( $q->num_rows() > 0 ) 
    {
        $enquiryData = array (
            'otp' => $otp
        );
        $this->db->where('mobile_number', $input['mobileNumber'])->update('enquiry_user', $enquiryData);
    } else {
        $enquiryData = array(
            'mobile_number' => $input['mobileNumber'],
            'otp' => $otp
        );
        $this->db->insert('enquiry_user', $enquiryData);
    }
  }

  public function check_registerd_number($mobile_num){
    $res1 = $this->db->where('mobile_no',$mobile_num)->get('parent')->row();
    $res2 = $this->db->where('preferred_contact_no',$mobile_num)->get('student_admission')->row();
    $res3 = $this->db->where('contact_number',$mobile_num)->get('staff_master')->row();
    if(empty($res1) && empty($res2) && empty($res3)){
        return 0;
    }
    return 1;   
  }

  public function verify_otp($mob_num,$otp){
    $sql = "SELECT * from enquiry_user where mobile_number = '$mob_num' AND  otp = '$otp'";
    $exist = $this->db->query($sql)->row();
        if ( $exist) {
            return 1;
        } else {
            return 0; 
        }
  }

  public function validate_date_of_birth($dob,$class_id){
    $q = $this->db->select("min_dob")
    ->from('class')
    ->where('id', $class_id)
    ->get()->row();

    if(empty($q->min_dob)){
      return 'Cut of Date Not added';
    }
    if(date("Y-m-d", strtotime($dob)) <= $q->min_dob){
      return 1;
    }
    return 'Not Eligible for this class';

  }

  public function generate_admission_form($enquiry_id, $expire_date){
    $enquiry_data = $this->db->select('*')->where('id',$enquiry_id)->get('enquiry')->row();
    $admission_id = '';
    $adm_data = $this->db->select('*')->where('enquiry_id',$enquiry_id)->get('admission_forms')->row();
      if($adm_data){
        if(isset($expire_date)) {
          $admnExpireDate = ($expire_date == '')? null : date("Y-m-d", strtotime($expire_date));
        } else {
          $admnExpireDate = null;
        }
        $this->db->where('id',$adm_data->id);
        $this->db->update('admission_forms',array('admission_link_expire_date'=>$admnExpireDate));
        $admission_id = 'Generated';
      }else{
        $admission_id = $this->_create_admission_form($enquiry_data, $expire_date);
      }
    return $admission_id;
  }

  private function _create_admission_form($enquiry_data, $expire_date) {
    $classTable = $this->db->select('class_name')->where('id',$enquiry_data->class_name)->get('class')->row()->class_name;
    
    $className = '';
    if(!empty($classTable)){
      $className = $classTable;
    }
    if($className == ''){
      return 'Class name not found';
    }

    $admissionSettings = $this->db->select('id, form_name, class_applied_for')
    ->from('admission_settings')
    ->where('acad_year',$enquiry_data->academic_year)
    ->get()->result();

    if(empty($admissionSettings)){
      return 'Admission forms not created';
    }

    $setting_id = 0;
    foreach ($admissionSettings as $key => $val) {
      $classArry = json_decode($val->class_applied_for);
        if (in_array($className, $classArry)) {
          $setting_id = $val->id;
          break;
        }
    }
    if($setting_id == 0){
      return 'Admission Settings ID Not Found'; 
    }
    $user_data = $this->db->where('mobile_no',$enquiry_data->mobile_number)->get('admission_user')->row();

    $this->db->trans_start();
    $admission_user = array(
      'mobile_no'=>$enquiry_data->mobile_number,
    );
    if(empty($user_data)){
      $this->db->insert('admission_user',$admission_user);
      $ad_user_id = $this->db->insert_id();
    }else{
      $this->db->where('id',$user_data->id);
      $this->db->update('admission_user',$admission_user);
      $ad_user_id = $user_data->id;
    }
    if(isset($expire_date)) {
      $admnExpireDate = ($expire_date == '')? null : date("Y-m-d", strtotime($expire_date));
    } else {
      $admnExpireDate = null;
    }
    $admission = array(
        'au_id'=>$ad_user_id,
        'academic_year_applied_for' => $enquiry_data->academic_year,
        'grade_applied_for'=> $classTable,
        'std_name'=> $enquiry_data->student_name,
        'student_last_name'=>$enquiry_data->student_last_name,
        'dob'=>($enquiry_data->student_dob == null) ? '' : $enquiry_data->student_dob,
        'gender'=>($enquiry_data->gender == null) ? '' : $enquiry_data->gender,
        'curriculum_currently_studying'=>$enquiry_data->board_opted,
        'boarding'=>$enquiry_data->boarding_type,
        'f_name'=>($enquiry_data->father_name == null) ? $enquiry_data->parent_name : $enquiry_data->father_name,
        'f_mobile_no'=>($enquiry_data->mobile_number == null) ? '' : $enquiry_data->mobile_number,
        'f_email_id'=>($enquiry_data->father_email_id == null) ? '' : $enquiry_data->father_email_id,
        'f_addr'=>($enquiry_data->residential_address == null)? '':$enquiry_data->residential_address,
        'f_county'=>($enquiry_data->current_country == null)? '':$enquiry_data->current_country,
        'f_profession'=>($enquiry_data->father_occupation == null) ? '' : $enquiry_data->father_occupation,
        'm_name'=>($enquiry_data->mother_name == null) ? '' : $enquiry_data->mother_name,
        'm_mobile_no'=>($enquiry_data->mother_phone_number == null) ? '' : $enquiry_data->mother_phone_number,
        'm_email_id'=>($enquiry_data->mother_email_id == null) ? '' : $enquiry_data->mother_email_id,
        'm_addr'=>($enquiry_data->residential_address == null) ? '' : $enquiry_data->residential_address,
        'm_county'=>($enquiry_data->current_country == null) ? '' : $enquiry_data->current_country,
        'm_profession'=>($enquiry_data->mother_occupation == null) ? '' : $enquiry_data->mother_occupation,
        's_present_addr'=>($enquiry_data->residential_address == null) ? '' : $enquiry_data->residential_address,
        's_present_area'=>($enquiry_data->current_city == null) ? '' : $enquiry_data->current_city,
        's_present_country'=>($enquiry_data->current_country == null) ? '' : $enquiry_data->current_country,
        'extracurricular_activities'=>$enquiry_data->interested_in,
        'has_sibling'=>$enquiry_data->has_sibling,
        'sibling_school_name'=>$enquiry_data->where_is_sibling,
        'sibling_student_name'=>$enquiry_data->sibling_name,
        'sibling_student_class'=>$enquiry_data->sibling_class,
        'student_mobile_no'=>$enquiry_data->student_phone_number,
        'student_email_id'=>$enquiry_data->student_email_id,
        'enquiry_id'=>$enquiry_data->id,
        'admission_setting_id'=> $setting_id,
        'transport'=> $enquiry_data->transportation_facility,
        'admission_link_expire_date' => $admnExpireDate
    );
    
    $this->db->insert('admission_forms',$admission);
    $admission_id = $this->db->insert_id();
    $adm_status = array(
      'af_id' =>$admission_id,
      'curr_status' =>'Draft',
    );
      $this->db->insert('admission_status',$adm_status);

      $this->db->trans_complete();
      if ($this->db->trans_status() === TRUE){
        return 'Generated';
      }else{
          return 'Something went wrong';
      }   
  }

  public function update_sent_admission_link($enquiry_id){
    $this->db->where('id',$enquiry_id);
    $this->db->update('enquiry',array('sent_admission_link'=>1));
  }

  public function isReporting_status_convert($status){
    $reporting_status = $this->db_readonly->select('reporting_status')->where('user_status',$status)->get('enquiry_internal_status_map')->row();
    if(!empty($reporting_status)){
      if($reporting_status->reporting_status == 'convert'){
        return 1;
      }else{
        return 0;
      }
    }else{
      return 0;
    }
   
  }

  public function get_enquiry_email_template($enquiry_id){        
    $email_template = $this->db_readonly->select("et.*,et.content as template_content,ifnull(et.email_subject, 'Email subject not added') as email_subject")
    ->from('email_template et')
    ->where('name','send_admission_link_to_parent')
    ->get()->row();
    if (!empty($email_template)) {
      $toEmail = $this->db_readonly->select('e.id as enId,ifnull(e.email,"") as parent_email,ifnull(e.mother_email_id,"") as mother_email_id,ifnull(e.father_email_id,"") as father_email_id, e.student_name,e.class_name,academic_year')
      ->from('enquiry e')
      ->where('e.id',$enquiry_id)
      ->get()->row();
      $email_template->to_email = $toEmail;
      $email_template->class_name = $this->db->select('class_name')->where('id',$toEmail->class_name)->get('class')->row()->class_name;
      $email_template->academic_year = $this->acad_year->getAcadYearById($toEmail->academic_year);
      $email_template->to_mails = $toEmail->parent_email.','.$toEmail->father_email_id.','.$toEmail->mother_email_id;
      return (array) $email_template;
    }else{
      return 0;
    }
    
  }

  public function update_follow_up($enquiry_id,$email_content,$followup_action,$follow_status){

    $enquiry_status = $this->db->select('status')->from('enquiry')->where('id',$enquiry_id)->get()->row()->status;
    if(empty($follow_status)){
      $follow_status = $enquiry_status;
    }
    $this->db->where('id',$enquiry_id);
    $this->db->update('enquiry',array('status' => $follow_status));

    $data = array(
        'follow_up_type'=>'Enquiry',
        'follow_up_action'=>($followup_action != '') ? $followup_action :'Email',
        'source_id' => $enquiry_id,
        'registered_email' => $email_content['registered_email'],
        'email_subject' => $email_content['email_subject'],
        'email_ids' => (($email_content['to_mails']) =='')? NULL:$email_content['to_mails'],
        'template_name' => $email_content['name'],
        'template_content' => $email_content['template_content'],
        'status' => $follow_status,
        'created_by' => $this->authorization->getAvatarId(), 
        'delivery_status' => 'Delivered', // check if email or sms
        'created_on' => $this->Kolkata_datetime(),
    );

    return $this->db->insert('follow_up',$data);
  }

  public function delete_otp($mobileNumber){
    $otp = $this->db->select('otp')->where('mobile_number',$mobileNumber)->get('enquiry_user')->row();
    if(empty($otp->otp)){
      return 0;
    } 
    $this->db->where('mobile_number',$mobileNumber);
    return $this->db->update('enquiry_user',array('otp'=>''));
  }

  public function enquiry_followup_status() {
    return $this->db->select('*')
    ->from('enquiry_internal_status_map')
    ->order_by('user_status')
    ->get()->result();
  }

  public function insert_enquiry_receipt_template(){
    $query = $this->db->get('enquiry_settings');
    if ($query->num_rows() > 0) {
      $this->db->where('id',$query->row()->id);
      $this->db->update('enquiry_settings',array('enquiry_print_template'=>$this->input->post('template')));
      return $this->db->affected_rows();
    }else{
      return $this->db->insert('enquiry_settings',array('enquiry_print_template'=>$this->input->post('template')));
    }
    
  }

  public function get_enquiry_receipt_template(){
    return $this->db->get('enquiry_settings')->row();
  }

  public function get_pdf_enquiry_details($enquiry_id_generate){
    return $this->db->select("e.*, c.class_name as clsname, date_format(e.student_dob, '%d-%b-%Y') as student_dob,  date_format(e.created_on, '%d-%b-%Y') as created_on, ifnull(e.father_name, '-') as father_name, ifnull(e.mother_name, '-') as mother_name, ifnull(e.mobile_number, '-') as mobile_number, ifnull(e.father_email_id, '-') as father_email_id, ifnull(e.mother_email_id, '-') as mother_email_id, ifnull(e.residential_address, '-') as residential_address, ifnull(e.student_current_school, '-') as student_current_school, ifnull(e.where_is_sibling, '-') as where_is_sibling, ifnull(e.sibling_name, '-') as sibling_name, ifnull(e.sibling_class, '-') as sibling_class, ifnull(e.transportation_facility, '-') as transportation_facility,concat(ifnull(sm.first_name,''),' ',concat(sm.last_name,'')) as counselor_name,TIMESTAMPDIFF(YEAR, e.student_dob, CURDATE()) as year, TIMESTAMPDIFF(MONTH, e.student_dob, CURDATE()) % 12 as month, FLOOR(TIMESTAMPDIFF(DAY, e.student_dob, CURDATE()) % 30.4375) AS day")
    ->from('enquiry e')
    ->join('class c','e.class_name=c.id', 'left')
    ->join('staff_master sm','e.assigned_to=sm.id','left')
    ->where('e.id',$enquiry_id_generate)
    ->get()->row();
  }

  public function get_pdf_enquiry_template() {
    return $this->db->select('enquiry_print_template')
    ->from('enquiry_settings')
    ->order_by('id','desc')
    ->get()->row();
  }

  public function update_enquiry_form_path($path, $enquiry_id_generate){
    $this->db->where('id',$enquiry_id_generate);
    return $this->db->update('enquiry', array('template_pdf_path'=> $path));
  }

  public function update_enquiry_PdfLink($path, $status){
    $this->db->where('template_pdf_path',$path);
    return $this->db->update('enquiry', array('pdf_status' => $status));
  }

  public function get_enquiry_form_pdf_path($enquiry_id_generate){
    // echo "<pre>"; print_r($enquiry_id_generate); die();
    $result = $this->db->select('template_pdf_path')
        ->where('id',$enquiry_id_generate)
        ->get('enquiry')->row();

        if (empty($result->template_pdf_path)) {
            return 0;
        }else{
            return $result->template_pdf_path;
        }
    }

    public function print_button($id) {
      $result = $this->db->select('template_pdf_path')
    ->from('enquiry e')
    ->where('id',$id)
    ->get()->row();
    if (!empty($result)) {
      if (!empty($result->template_pdf_path)) {
        return 1;
      }else{
        return 0;
      }
      return 0;
    }
    }

    public function get_admission_expire_date_by_enquiry_id($enquiryId){
      $result = $this->db->select('date_format(admission_link_expire_date,"%d-%m-%Y") as admission_link_expire_date')
      ->from('admission_forms')
      ->where('enquiry_id',$enquiryId)
      ->get()->row();
      if(!empty($result)){
        return $result->admission_link_expire_date;
      }else{
        return '';
      }
    }
    

    function insert_enqiry_data_next_yr($enquiry_id,$remarks){
      $this->db->trans_start();
      $present_yr_data= $this->db->get_where('enquiry', array('id'=>$enquiry_id))->row_array();

      $follow_ups = $this->db->select('*')->from('follow_up')->where('source_id',$enquiry_id)->where('follow_up_type','Enquiry')->get()->result();

      unset($present_yr_data['id']);
      
      $present_yr_data['academic_year']= $present_yr_data['academic_year']+1;
      //$present_yr_data['status']= 'Closed-not interested';
     // echo "<pre>"; print_r($present_yr_data); die();
     //echo "<pre>";print_r($present_yr_data);
      $table ='class';
      if($this->settings->getSetting('custom_class_from_enquiry_class_table')){
        $table ='enquiry_class';
      }
      $currentClass= $this->db->select('class_name')
      ->from($table)
      ->where('id',$present_yr_data['class_name'])
      ->get()->row();
      //echo "<pre>";print_r($currentClass);
      $nextClass ='';
      if(!empty($currentClass)){
        $nextClass= $this->db->select('id')
        ->from($table)
        ->where('class_name',$currentClass->class_name)
        ->where('acad_year_id', $present_yr_data['academic_year'])
        ->get()->row()->id;
      }
      //echo "<pre>";print_r($nextClass); die();
      if(empty($nextClass)){
        return 2; // Next Year Class not defined.
      }
      $present_yr_data['class_name'] = $nextClass;
      $this->db->insert('enquiry', $present_yr_data);
      $new_enquiry_id = $this->db->insert_id();

      // echo '<pre>';print_r($new_enquiry_id);
      if (!empty($follow_ups)) {
        foreach($follow_ups  as $key => $val){
          $val->source_id = $new_enquiry_id;
          unset($val->id);
        }
        $this->db->insert_batch('follow_up',$follow_ups);
      }

      $data = array(
        'follow_up_type'=> 'Enquiry',
        'source_id'=>$enquiry_id,
        'remarks'=>$remarks,
        'status'=>'Closed-not interested',
        'follow_up_action' => 'Status-update',

      );

      $followup = $this->db->insert('follow_up',$data);
      
      $this->db->where('id',$enquiry_id);
      $this->db->update('enquiry',array('status'=>'Closed-not interested'));

      $this->db->trans_complete();

    return $this->db->trans_status();

    }

    public function get_students_based_on_status($input){
      $this->db->select('e.id,student_name,c.class_name')
      ->from('enquiry e')
      ->join('class c','e.class_name=c.id')
      ->where('e.academic_year',$this->acad_year->getAcadYearID());
   
      if($input['follow_up_status']){
        $this->db->where_in('e.status',$input['follow_up_status']);
      }
      if($input['grade']){
        $this->db->where_in('e.class_name',$input['grade']);
      }
      // if ($input['follow_up_status'] && $input['status_from_date'] && $input['status_to_date']) {
      //     $this->db->where('date_format(e.created_on, "%Y-%m-%d") between "'.date('Y-m-d', strtotime($input['status_from_date'])).'" and "'.date('Y-m-d', strtotime($input['status_to_date'])).'"');
      // }
      $this->db->order_by('e.student_name');

    return $this->db->get()->result();
}

    public function getStudents($enquiry_ids){
      return $this->db->select("id,student_name,ifnull(email,'') as email,ifnull(mother_email_id,'') as mother_email_id,ifnull(father_email_id,'') as father_email_id,mobile_number,father_phone_number,mother_phone_number,student_phone_number")
      ->from('enquiry')
      ->where('academic_year',$this->acad_year->getAcadYearID())
      ->where_in('id',$enquiry_ids)
      ->get()->result();
    }

    public function update_mass_followup_status($enquiry_ids,$status,$follow_up_action,$remarks){
      $this->db->trans_start();
      foreach($enquiry_ids as $id){
     
      $enquiry = array(
          'status'=>$status
      );
      $this->db->where('id',$id);
      $this->db->update('enquiry',$enquiry);

      $data = array(
          'follow_up_type' => 'Enquiry', 
          'follow_up_action' =>$follow_up_action,
          'source_id' => $id,
          'status' =>  $status, 
          'created_by' => $this->authorization->getAvatarId(), 
          'remarks' => $remarks,
          'created_on' => $this->Kolkata_datetime(),
        );
      $this->db->insert('follow_up',$data);
      }
      $this->db->trans_complete();
      return $this->db->trans_status();
    }

    public function submit_follow_up($enquiry_id,$remarks,$from_email,$email_body,$title,$email_sent_to_ids,$parents_emails){
      $email_id = $this->db->select("status")
      ->from('enquiry')
      ->where('id',$enquiry_id)
      ->get()->row();

      $data = array(
          'follow_up_type' => 'Enquiry', 
          'follow_up_action' =>'Email',
          'source_id' => $enquiry_id,
          'status' =>  $email_id->status, 
          'created_by' => $this->authorization->getAvatarId(), 
          'remarks' => $remarks,
          'created_on' => $this->Kolkata_datetime(),
          'email_subject'=>$title,
          'email_ids'=>$parents_emails,
          'template_content'=>$email_body,
          'delivery_status'=>'Delivered',
          'registered_email'=>$from_email,
          'email_sent_to_ids'=>$email_sent_to_ids
      );
      return $this->db->insert('follow_up',$data);
    }

    public function get_sms_content_by_id($name){
       return $this->db_readonly->select('stn.id, stn.name, stn.category, stay.content, stay.acad_year_id')
        ->from('sms_template_new stn')
        ->join('sms_templates_acad_year stay','stn.id=stay.sms_templates_id')
        ->where('name',$name)
        ->get()->row();
    }

    public function get_student_enquiry_data($enquiry_ids){
      $result = $this->db_readonly->select('e.id,student_name,c.class_name,mobile_number,alternate_mobile_number,father_phone_number,mother_phone_number,student_phone_number')
        ->from('enquiry e')
        ->join('class c','e.class_name=c.id')
        ->where_in('e.id',$enquiry_ids)
        ->get()->result();
      
        foreach($result as $key=>$val){
          $val->mobile_numbers = array();
          array_push($val->mobile_numbers,$val->mobile_number);
          array_push($val->mobile_numbers,$val->alternate_mobile_number);
          array_push($val->mobile_numbers,$val->father_phone_number);
          array_push($val->mobile_numbers,$val->mother_phone_number);
          array_push($val->mobile_numbers,$val->student_phone_number);
        }

        return $result;
    }

    public function load_caste(){
      return $this->db_readonly->select("*")
        ->from("student_caste_list")
        ->get()->result();
    }

    public function save_sending_email_data($sent_data) {
        $this->db->insert('email_sent_to', $sent_data);
        return $this->db->insert_id();
    }

    public function get_feeTemplate_by_classId($en_id){
      return $this->db->select('fee_structure_template')
      ->from('enquiry e')
      ->join('class c','e.class_name=c.id')
      ->where('e.id',$en_id)
      ->get()->row()->fee_structure_template;
    }

    public function get_class_names_from_master(){
      return $this->db->select('class_name')->from('class_master')->where('LOWER(short_name) !=', 'ph')->order_by('display_order')->get()->result();
    }

    public function  get_sources() {
      return $this->db->select('distinct(source)')->from('enquiry')->where('source !=','')->get()->result();
    }

    public function get_class_names_from_class_table(){
      return $this->db->select('id,class_name')->from('class')->where('acad_year_id',$this->acad_year->getAcadYearID())->get()->result();
    }

    public function get_reffered_student_list($class_id){
      return $this->db->select('admission_no,concat(ifnull(sa.first_name,"")," ",ifnull(sa.last_name,"")) as student_name')
      ->from('student_admission sa')
      ->join('student_year sy','sa.id=sy.student_admission_id','left')
      ->where('sy.class_id',$class_id)
      ->where('sa.admission_status','2')
      ->where('sy.promotion_status!=','4')
      ->where('sy.acad_year_id',$this->acad_year->getAcadYearID())
      ->get()->result();
    }

    public function get_duplicate_enquiries($column_names) {
      $subqueries = [];
  
      // Loop through each column name to build subqueries
      foreach ($column_names as $column_name) {
          // Subquery to find duplicate values for the current column
          $subquery = $this->db->select($column_name)
                               ->from('enquiry as sub')
                               ->where('sub.academic_year', $this->acad_year->getAcadYearID())
                               ->group_by($column_name)
                               ->having('COUNT('.$column_name.') >', 1)
                               ->get_compiled_select();
  
          // Store the subquery for later use
          $subqueries[] = "e.$column_name IN ($subquery)";
      }
  
      // Combine all subqueries using OR condition
      $where_clause = implode(' OR ', $subqueries);
  
      // Final query to get all records matching the duplicate values
      $table = 'class';
      if($this->settings->getSetting('custom_class_from_enquiry_class_table')){
        $table ='enquiry_class';
      }
      $result = $this->db->select("e.id, e.student_name, IFNULL(c.class_name, '-') as class_name, IFNULL(e.enquiry_number, '') as enquiry_number, e.email, e.mobile_number, e.status")
                         ->from('enquiry e')
                         ->join($table.' c', 'e.class_name = c.id', 'left')
                         ->where('e.academic_year', $this->acad_year->getAcadYearID())
                         ->where("($where_clause)", null, false) // Combine all subqueries
                         ->order_by('e.student_name', 'ASC')
                         ->get()
                         ->result();
  
      return $result;
  }

  public function update_duplicate_enquiry(){
    $invalid_status =  $this->db->select('user_status')->from('admission_internal_status_map')->where('reporting_status','invalid')->get()->row();

    if(empty($invalid_status)){
      return -1;
    }
    $this->db->trans_start();
      $this->db->where('id',$this->input->post('enquiry_id'));
      $this->db->update('enquiry',array('status'=>$invalid_status->user_status));

      $follow_up = array(
        'follow_up_type' => 'Enquiry',
        'source_id' => $this->input->post('enquiry_id'),
        'status' => 'Duplicate',
        'created_on' =>$this->Kolkata_datetime(),
        'created_by' => $this->authorization->getAvatarId(), 
        'remarks' => $this->input->post('remarks') ? $this->input->post('remarks') : ''
      );
      $this->db->insert('follow_up',$follow_up);
      
      $this->db->trans_complete();

      if($this->db->trans_status()){
        return $invalid_status->user_status;
      }else{
        return 0;
      }
  }

  public function get_enquiry_record(){
    if(isset($_POST['student_dob']) && isset($_POST['mobile_number']) && isset($_POST['parent_name'])){
      $result = $this->db->select('id')
    ->from('enquiry')
    ->where('student_name', $this->input->post('student_name', true)) // Prevent XSS
    ->where('mobile_number', $this->input->post('mobile_number', true))
    ->where('student_dob', date('Y-m-d', strtotime($this->input->post('student_dob', true))))
    ->where('parent_name', $this->input->post('parent_name', true))
    ->get()
    ->row();
      if(!empty($result)){
        return 1;
      }
    }
    return 0;
  }

  public function undo_dedupe_enquiry(){
    $this->db->where('id',$this->input->post('enquiry_id'));
    $this->db->update('enquiry',array('status'=>'Created'));

    $follow_up = array(
      'follow_up_type' => 'Enquiry',
      'source_id' => $this->input->post('enquiry_id'),
      'status' => 'Created',
      'created_on' =>$this->Kolkata_datetime(),
      'created_by' => $this->authorization->getAvatarId()
    );
    return $this->db->insert('follow_up',$follow_up);
}

public function get_invalid_status(){
  $status = $this->db_readonly->select('user_status')->from('admission_internal_status_map')->where('reporting_status','invalid')->get()->row();

  if(!empty($status)){
    return $status->user_status;
  }

  return '';
}
  
}

?>
