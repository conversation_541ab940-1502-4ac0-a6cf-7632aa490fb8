<div class="card" style="border-radius: 8px;height: 31rem;margin-bottom: 8px;" id="student_attendance_day_v2_summary_prototype">
	<div class="card-header panel_heading_new_style_staff" style="border-top-left-radius: 8px;border-top-right-radius: 8px">
		<div class="card-title card-title-new-style">
			Student Day Attendance V2
			<div class="pull-right">
				<a href="<?php echo site_url('attendance_day_v2/Attendance_day_v2/takeAttendance'); ?>">
					<span class="fa fa-list"></span>
				</a>
			</div>
		</div>
	</div>
	<div class="card-body pt-0">
		<?php
		// Sample data for prototype demonstration (remove this when integrating with real data)
		if (!isset($present)) $present = 98;
		if (!isset($absent)) $absent = 9;
		if (!isset($attendance_type)) $attendance_type = 'normal';

		// Check user permissions (in real implementation, this comes from authorization)
		if (!isset($has_all_section_permission)) {
			$has_all_section_permission = true; // Set to false to test single class view
		}

		if (!isset($class_wise_data)) {
			if ($has_all_section_permission) {
				// User has TAKE_ALL_SECTION_ATTENDANCE privilege - show ALL classes
				$class_wise_data = [
					[
						'class_name' => 'Grade 1',
						'total_present' => 45,
						'total_absent' => 5,
						'sections' => [
							['section_name' => 'A', 'present' => 25, 'absent' => 2],
							['section_name' => 'B', 'present' => 20, 'absent' => 3],
							['section_name' => 'C', 'present' => 0, 'absent' => 0] // Not taken
						]
					],
					[
						'class_name' => 'Grade 2',
						'total_present' => 53,
						'total_absent' => 4,
						'sections' => [
							['section_name' => 'A', 'present' => 28, 'absent' => 2],
							['section_name' => 'B', 'present' => 25, 'absent' => 2],
							['section_name' => 'C', 'present' => 0, 'absent' => 0] // Not taken
						]
					],
					[
						'class_name' => 'Grade 3',
						'total_present' => 0,
						'total_absent' => 0,
						'sections' => [
							['section_name' => 'A', 'present' => 0, 'absent' => 0], // Not taken
							['section_name' => 'B', 'present' => 0, 'absent' => 0]  // Not taken
						]
					]
				];
			} else {
				// User doesn't have privilege - show only THEIR assigned class/sections
				$class_wise_data = [
					[
						'class_name' => 'Grade 2', // Only their assigned class
						'total_present' => 53,
						'total_absent' => 4,
						'sections' => [
							['section_name' => 'A', 'present' => 28, 'absent' => 2], // Their section
						]
					]
				];
			}
		}
		?>
		<?php if (isset($attendance_type)) { ?>
			<?php if ($attendance_type == 'holiday') { ?>
				<div class="text-center" style="padding: 80px 20px;">
					<i class="fa fa-calendar fa-4x text-info mb-3"></i>
					<h5 class="text-info">Holiday</h5>
					<p class="text-muted"><?php echo $attendance_message; ?></p>
				</div>
			<?php } elseif ($attendance_type == 'event') { ?>
				<div class="text-center" style="padding: 80px 20px;">
					<i class="fa fa-star fa-4x text-warning mb-3"></i>
					<h5 class="text-warning">Special Event</h5>
					<p class="text-muted"><?php echo $attendance_message; ?></p>
				</div>
			<?php } elseif ($attendance_type == 'no_sections') { ?>
				<div class="text-center" style="padding: 80px 20px;">
					<i class="fa fa-info-circle fa-4x text-secondary mb-3"></i>
					<h5 class="text-secondary">No Sections Assigned</h5>
					<p class="text-muted"><?php echo $attendance_message; ?></p>
				</div>
			<?php } elseif ($attendance_type == 'no_permission') { ?>
				<div class="text-center" style="padding: 80px 20px;">
					<i class="fa fa-lock fa-4x text-warning mb-3"></i>
					<h5 class="text-warning">Access Denied</h5>
					<p class="text-muted"><?php echo $attendance_message; ?></p>
				</div>
			<?php } else { ?>
				<!-- Main Content Wrapper -->
				<div style="padding: 15px; background: #f8f9fa; border-radius: 8px; margin: 15px 0;">
					<!-- Summary Statistics -->
					<div class="d-flex justify-content-around mb-3">
						<div class="text-center">
							<div style="font-size: 24px; font-weight: bold; color: #28a745;">
								<?php echo isset($present) ? $present : 0; ?>
							</div>
							<small class="text-muted">Present</small>
						</div>
						<div class="text-center">
							<div style="font-size: 24px; font-weight: bold; color: #dc3545;">
								<?php echo isset($absent) ? $absent : 0; ?>
							</div>
							<small class="text-muted">Absent</small>
						</div>
						<div class="text-center">
							<div style="font-size: 24px; font-weight: bold; color: #6c757d;">
								<?php
								$total_students = (isset($present) ? $present : 0) + (isset($absent) ? $absent : 0);
								echo $total_students;
								?>
							</div>
							<small class="text-muted">Total</small>
						</div>
					</div>

					<!-- Attendance Chart -->
					<div id="attendance_chart" style="height: 180px; margin: 20px 0;"></div>

					<!-- Legend -->
					<div class="d-flex justify-content-center mb-3">
						<div class="d-flex flex-wrap justify-content-center">
							<span class="mx-2">
								<i class="fa fa-square" style="color: #28a745;"></i> Present
							</span>
							<span class="mx-2">
								<i class="fa fa-square" style="color: #dc3545;"></i> Absent
							</span>
						</div>
					</div>

					<!-- Section Details -->
					<div style="border-top: 1px solid #dee2e6; padding-top: 15px;">
						<!-- Permission Level Indicator -->
						<div class="mb-3" style="background: <?php echo $has_all_section_permission ? '#e7f3ff' : '#fff3cd'; ?>; padding: 8px 12px; border-radius: 6px; border-left: 4px solid <?php echo $has_all_section_permission ? '#007bff' : '#ffc107'; ?>;">
							<small style="color: <?php echo $has_all_section_permission ? '#004085' : '#856404'; ?>; font-weight: 600;">
								<i class="fa <?php echo $has_all_section_permission ? 'fa-users' : 'fa-user'; ?> mr-1"></i>
								<?php if ($has_all_section_permission) { ?>
									Viewing: All Classes & Sections (Full Access)
								<?php } else { ?>
									Viewing: Your Assigned Classes Only (Limited Access)
								<?php } ?>
							</small>
						</div>

						<?php if (isset($class_wise_data) && !empty($class_wise_data)) { ?>
							<!-- Class-wise data available -->
							<div style="max-height: 200px; overflow-y: auto;">
								<?php foreach ($class_wise_data as $class_key => $class_data) { ?>
									<div class="mb-3" style="background: #f8f9fa; padding: 10px; border-radius: 8px;">
										<!-- Class Header -->
										<div class="mb-2">
											<strong style="color: #495057; font-size: 14px;">
												<?php echo $class_data['class_name']; ?>
												<small class="text-muted ml-2">
													(P: <?php echo $class_data['total_present']; ?> | A: <?php echo $class_data['total_absent']; ?>)
												</small>
											</strong>
										</div>
										<!-- Sections -->
										<div class="d-flex flex-wrap">
											<?php foreach ($class_data['sections'] as $section_key => $section_data) { ?>
												<?php
												$total_students = $section_data['present'] + $section_data['absent'];
												$attendance_taken = $total_students > 0;
												$badge_color = $attendance_taken ? '#28a745' : '#dc3545';
												$status_text = $attendance_taken ? 'TAKEN' : 'NOT TAKEN';
												$icon_class = $attendance_taken ? 'fa-check-circle' : 'fa-times-circle';
												?>
												<div class="mr-2 mb-2" style="background: <?php echo $badge_color; ?>; color: white; border-radius: 8px; padding: 8px 12px; font-size: 11px;">
													<div class="d-flex align-items-center">
														<strong>Section <?php echo $section_data['section_name']; ?></strong>
														<i class="fa <?php echo $icon_class; ?> ml-2"></i>
													</div>
													<div style="font-size: 10px; margin-top: 2px;">
														<?php if ($attendance_taken) { ?>
															<span>Present: <?php echo $section_data['present']; ?> | Absent: <?php echo $section_data['absent']; ?></span>
														<?php } else { ?>
															<span><?php echo $status_text; ?></span>
														<?php } ?>
													</div>
												</div>
											<?php } ?>
										</div>
									</div>
								<?php } ?>
							</div>
						<?php } else { ?>
							<!-- Fallback when no class data available -->
							<div class="text-center" style="padding: 30px 0;">
								<i class="fa fa-info-circle fa-2x text-muted mb-2"></i>
								<div style="color: #6c757d; font-size: 14px;">
									Class details not available
								</div>
								<small class="text-muted">
									<?php if (!isset($class_wise_data)) { ?>
										Data not loaded
									<?php } else { ?>
										No classes assigned
									<?php } ?>
								</small>
							</div>
						<?php } ?>
					</div>
				</div>
			<?php } ?>
		<?php } ?>
	</div>
</div>

<!-- Morris.js Chart Library -->
<link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/morris.js/0.5.1/morris.css">
<script src="//cdnjs.cloudflare.com/ajax/libs/raphael/2.1.0/raphael-min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/morris.js/0.5.1/morris.min.js"></script>

<script>
$(document).ready(function() {
	// Create chart with attendance data
	var presentCount = <?php echo isset($present) ? $present : 0; ?>;
	var absentCount = <?php echo isset($absent) ? $absent : 0; ?>;

	console.log('Present:', presentCount, 'Absent:', absentCount); // Debug log
	console.log('Class data available:', <?php echo isset($class_wise_data) ? 'true' : 'false'; ?>);
	<?php if (isset($class_wise_data)) { ?>
		console.log('Class data count:', <?php echo count($class_wise_data); ?>);
	<?php } ?>

	// Clear any existing content first
	$('#attendance_chart').empty();

	// Only create chart if there's data
	if (presentCount > 0 || absentCount > 0) {
		try {
			// Add a small delay to ensure DOM is ready
			setTimeout(function() {
				new Morris.Donut({
					element: 'attendance_chart',
					data: [
						{ label: 'Present', value: presentCount },
						{ label: 'Absent', value: absentCount }
					],
					colors: ['#28a745', '#dc3545'],
					resize: true,
					formatter: function (y) { return y + ' students'; }
				});
			}, 100);
		} catch (error) {
			console.error('Chart creation error:', error);
			$('#attendance_chart').html('<div class="text-center" style="padding: 60px 0;"><i class="fa fa-chart-pie fa-3x text-muted mb-3"></i><br><span class="text-muted">Chart loading error</span></div>');
		}
	} else {
		$('#attendance_chart').html('<div class="text-center" style="padding: 60px 0;"><i class="fa fa-info-circle fa-3x text-muted mb-3"></i><br><span class="text-muted">No attendance data available</span></div>');
	}
});
</script>

<style>
/* Widget compatibility styles */
#student_attendance_day_v2_summary_prototype {
	overflow: hidden;
}

#student_attendance_day_v2_summary_prototype .card-body {
	height: calc(31rem - 60px);
	overflow-y: auto;
}

/* Chart container */
#attendance_chart {
	width: 100%;
}

#attendance_chart svg {
	left: 0% !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
	#student_attendance_day_v2_summary_prototype {
		height: auto !important;
		min-height: 300px;
	}

	#student_attendance_day_v2_summary_prototype .card-body {
		height: auto;
		max-height: 400px;
	}

	#student_attendance_day_v2_summary_prototype .d-flex {
		flex-direction: column;
	}

	#student_attendance_day_v2_summary_prototype .d-flex > div {
		margin-bottom: 10px;
	}
}

/* Badge hover effects */
.badge {
	transition: all 0.3s ease;
}

.badge:hover {
	transform: scale(1.05);
}

/* Summary statistics hover effects */
.d-flex.justify-content-around > div {
	transition: all 0.3s ease;
}

.d-flex.justify-content-around > div:hover {
	transform: translateY(-2px);
}
</style>