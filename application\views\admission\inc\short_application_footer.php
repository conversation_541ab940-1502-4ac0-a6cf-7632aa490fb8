<?php 
$admission_ui_colors = [];
$ui_colors_array = $this->settings->getSetting('admissions_ui_theme_color');

if (!empty($ui_colors_array)) {
    $admission_ui_colors = array_column($ui_colors_array, 'value', 'name');
}
?>
<?php if ($this->mobile_detect->isTablet() || $this->mobile_detect->isMobile()) { ?>
<div style="background-color: #CBBCFF; padding: 8px 0;">
    <div class="text-center">
        <p style="color: black; margin: 0; font-size: 14px;">
            Powered by <?php echo $this->settings->getSetting('company_name'); ?>
        </p>
    </div>
</div>
<?php } else { ?>
<div style="background-color: #CBBCFF; padding: 7px 0;margin-top:10rem">
    <div style="text-align: center; white-space: nowrap; overflow: hidden;;width:100%">
        <p style="color: black; margin: 0; font-size: 14px;">
            Powered by <strong><?php echo $this->settings->getSetting('company_name'); ?></strong>
        </p>
    </div>
</div>
<?php } ?>
</div>
<style type="text/css">
.message-box .mb-container {
    background: #fff;
}

.x-navigation li.active>a {
    background: #00701a;
}

@media (max-width: 768px) {
    .message-box .mb-container .mb-middle {
        width: 100%;
        padding: 0;
        margin-left: -25%;
    }
}

.container-fluid {
    display: flex;
    width: 1440px;
    height: 38px;
    padding: 10px 635px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    flex-shrink: 0;
}
</style>
<!-- END MESSAGE BOX-->

<!-- START PRELOADS -->
<!-- <audio id="audio-alert" src="<?php echo base_url();?>audio/alert.mp3" preload="auto"></audio>
        <audio id="audio-fail" src="<?php echo base_url();?>audio/fail.mp3" preload="auto"></audio> -->
<!-- END PRELOADS -->

<!-- START SCRIPTS -->

<!-- START PLUGINS -->
<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/jquery/jquery.min.js"></script>


<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/jquery/jquery-ui.min.js"></script>
<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/bootstrap/bootstrap.min.js"></script>
<!-- END PLUGINS -->

<!-- START THIS PAGE PLUGINS-->
<script type='text/javascript' src='<?php echo base_url();?>assets/js/plugins/icheck/icheck.min.js'></script>
<script type="text/javascript"
    src="<?php echo base_url();?>assets/js/plugins/mcustomscrollbar/jquery.mCustomScrollbar.min.js"></script>
<!-- <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/scrolltotop/scrolltopcontrol.js"></script> -->
<!-- <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/morris/raphael-min.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/morris/morris.min.js"></script>        -->
<!-- <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/rickshaw/d3.v3.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/rickshaw/rickshaw.min.js"></script> -->
<!-- <script type='text/javascript' src='<?php echo base_url();?>assets/js/plugins/jvectormap/jquery-jvectormap-1.2.2.min.js'></script>
        <script type='text/javascript' src='<?php echo base_url();?>assets/js/plugins/jvectormap/jquery-jvectormap-world-mill-en.js'></script>                 -->
<script type='text/javascript' src='<?php echo base_url();?>assets/js/plugins/bootstrap/bootstrap-datepicker.js'>
</script>
<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/owl/owl.carousel.min.js"></script>
<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/moment.min.js"></script>
<!-- <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/daterangepicker/daterangepicker.js"></script> -->
<!-- <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/bootstrap/bootstrap-file-input.js"></script> -->
<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/bootstrap/bootstrap-select.js"></script>
<!-- <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/tagsinput/jquery.tagsinput.min.js"></script> -->
<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/bootstrap/bootbox.min.js"></script>
<!-- END THIS PAGE PLUGINS-->

<!--  For Datatables -->
<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/datatables/jquery.dataTables.min.js">
</script>
<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/tableexport/tableExport.js"></script>
<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/tableexport/jquery.base64.js"></script>
<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/tableexport/html2canvas.js"></script>
<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/tableexport/jspdf/libs/sprintf.js">
</script>
<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/tableexport/jspdf/jspdf.js"></script>
<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/tableexport/jspdf/libs/base64.js">
</script>
<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/bootstrap/bootstrap-timepicker.min.js">
</script>
<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins.js"></script>
<script type="text/javascript" src="<?php echo base_url();?>assets/js/actions.js"></script>
<!-- <script type="text/javascript" src="<?php echo base_url();?>assets/js/demo_dashboard.js"></script> -->
<!-- END TEMPLATE -->

<script type="text/javascript" src="<?php echo base_url();?>assets/js/parsley.js"></script>
<script type="text/javascript" src="<?php echo base_url();?>assets/js/bootstrap-datetimepicker.js"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/common/multiselect.min.js');?>"></script>
<!-- <script type='text/javascript' src='<?php echo base_url();?>assets/js/chung-timepicker.js'></script>  -->
<script type='text/javascript' src='<?php echo base_url();?>assets/js/pnotify.js'></script>
<script type='text/javascript' src='<?php echo base_url();?>assets/js/pnotify.buttons.js'></script>

<!--For Color Picker (Used in Subjects)-->
<!-- <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/bootstrap/bootstrap-colorpicker.js"></script> -->
<!-- END SCRIPTS -->

<!--For Calendar-->
<script type="text/javascript" src="<?php echo base_url();?>assets/js/monthly.js"></script>
<!-- End For Calendar-->

<script type="text/javascript">
$('#demo-form').parsley();
</script>
<script type="text/javascript">
var $form = $('#demo-form');
$('.submitFormSingleClick').click(function() {
    if ($form.parsley().validate()) {
        //console.log ( 'valid' );
        $(this).val('Please wait ...').attr('disabled', 'disabled');
        $('#demo-form').submit();
    }
});
</script>
</body>

</html>