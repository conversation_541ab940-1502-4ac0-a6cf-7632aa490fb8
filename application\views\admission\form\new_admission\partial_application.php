<style>
.required {
    color: red;
}



<?php if ($this->mobile_detect->isTablet() || $this->mobile_detect->isMobile()) {
    ?>.preview-container {
        max-width: 900px;
        margin: 0 auto;
        background-color: #fff;
        border-radius: 12px;
        /* padding: 40px; */
    }

    .img-rounded {
        width: 30px;
        height: 20px;
    }

    .school_name_in_header {
        font-size: 15px !important;
    }

    .container {
        background-color: #FFF;
        border-radius: 24px;
        padding: 0;
        margin: 0 10px
    }

    <?php
}

else {
    ?>.preview-container {
        max-width: 70%;
        margin: 0 auto;
        background-color: #fff;
        border-radius: 12px;
        padding: 40px;
    }

    .section-header {
        width: 100%;
        height: 70px;
        /* background: #EFECFD; */
        border-radius: 24px 24px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 15px;
    }

    .container {
        margin-left: 20px !important;
        background-color: #FFF;
        border-radius: 24px;
        padding: 0;
        width: 98%;
    }

    <?php
}

?>.section-title {
    font-weight: 600;
    margin-left: 32px;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
}

.section-note {
    font-size: 15px;
    margin-right: 32px;
}

.parsley-errors-list {
    color: red;
    list-style: none;
    padding-left: 0;
    margin-top: 5px;
    font-size: 13px;
}

input.parsley-error,
select.parsley-error,
textarea.parsley-error {
    border: 1.5px solid #EE443F;
    background-color: #FDECEC !important;
}
</style>
<?php 
$admission_ui_colors = [];
$ui_colors_array = $this->settings->getSetting('admissions_ui_theme_color');

if (!empty($ui_colors_array)) {
    $admission_ui_colors = array_column($ui_colors_array, 'value', 'name');
}
?>
<div class="" style="background-color:#F9F7FE;font-style: normal;">
    <form enctype="multipart/form-data" id="final-form" class="form-horizontal" data-parsley-validate method="post"
        action="<?php echo site_url('admission_controller/InsertStudentDetails') ?>">
        <input type="hidden" name="au_id" id="au_id" value="<?= $au_id ?>">
        <input type="hidden" name="admission_setting_id" id="admission_setting_id" value="<?= $admission_setting_id ?>">
        <input type="hidden" name="af_id" id="af_id" value="<?= $af_id ?>">
        <input type="hidden" name="academic_year" id="academic_year" value="<?= $config_val['acad_year'] ?>">
        <div style="background-color: #F9F7FE; padding: 20px 22px;">
            <div style="display: flex; align-items: center; gap: 12px;">
                <!-- Back Icon -->
                <a href="<?php echo site_url('admissions/home') ?>" style="display: flex; align-items: center;">
                    <div style="width: 16px; height: 16px;">
                        <?php $this->load->view('svg_icons/back_icon.svg') ?>
                    </div>
                </a>

                <!-- Title -->
                <span style="font-weight: 500; font-size: 18px; color: #111;">
                    <?= $config_val['form_name'] . ' ' . $config_val['form_year'] ?>
                </span>

                <!-- Info Icon -->
                <a href="javascript:void(0);" onclick="get_instructions('<?= $admission_setting_id ?>')"
                    style="display: flex; align-items: center;">
                    <div style="width: 18px; height: 18px; cursor: pointer;">
                        <?php $this->load->view('svg_icons/info.svg'); ?>
                    </div>
                </a>
            </div>
        </div>




        <div class="container mt-4">

            <div class="section-header" style="width: 100%;display: flex; align-items: center; gap: 12px;background: <?php echo !empty($admission_ui_colors['secondary_background_color']) ? $admission_ui_colors['secondary_background_color']: '#EFECFD'?>;color:<?php echo !empty($admission_ui_colors['secondary_font_color']) ? $admission_ui_colors['secondary_font_color'] :  '#000' ?>">
                <span class="section-title">Student Information</span>
                <span class="section-note small">
                    <span class="text-danger">*</span> Fields mandatory to fill
                </span>
            </div>
            <div class="preview-container">
                <div class="row">
                    <label class="col-md-3 col-form-label" style="font-size:15px">Student Name <span
                            class="text-danger">&nbsp *</span></label>
                    <div class="col-md-6">
                        <input type="text" class="form-control" placeholder="Enter First Name" id="student_firstname"
                            name="student_firstname" required=""
                            data-parsley-error-message="Cannot be empty, only alphabets"
                            data-parsley-pattern="^[a-zA-Z. ]+$" data-parsley-minlength="2"
                            <?php if(isset($adm_data) && !empty($adm_data->std_name)) { echo 'value="'.$adm_data->std_name.'"'; } ?>>
                    </div>
                    <?php if(!in_array('student_last_name',$admission_dispay_fields)) { ?>
                    <div class="col-md-3">
                        <input type="text" class="form-control" placeholder="Enter Last Name" class="form-control"
                            placeholder="Enter Last Name" id="student_last_name" name="student_last_name"
                            data-parsley-error-message="Cannot be empty, only alphabets"
                            data-parsley-pattern="^[a-zA-Z. ]+$"
                            <?php echo $admission_required_fields['student_last_name']['required'] ?>
                            <?php if(isset($adm_data) && !empty($adm_data->student_last_name)) { echo 'value="'.$adm_data->student_last_name.'"'; } ?>>
                    </div>
                    <?php }?>
                </div>

                <?php
                    $dob = '';
                    if (!empty($adm_data->dob)) {
                        $dob = date('Y-m-d', strtotime($adm_data->dob)); // convert to YYYY-MM-DD
                    }
                ?>

                <div class="row">
                    <label class="col-md-3 col-form-label" style="font-size:15px">
                        Date of Birth <span class="text-danger">&nbsp*</span>
                    </label>
                    <div class="col-md-9">
                        <input type="date" required class="form-control" id="student_dob" name="student_dob"
                        placeholder="Enter date of birth" data-parsley-required-message="Date of birth is required"
                        value="<?php echo $dob; ?>">
                    </div>
                </div>

                <!-- Gender Selection -->
                <div class="row align-items-center mb-3">
                    <label class="col-md-3 col-form-label" style="font-size: 15px;">
                        Gender <span class="text-danger">&nbsp;*</span>
                    </label>
                    <div class="col-md-9">
                        <div class="row g-3 align-items-start gender_btn">
                            <div class="col-md-4" style="padding-left:0px">
                                <input type="radio" class="btn-check" name="gender" id="gender-male" value="M"
                                    <?php if (!isset($adm_data->gender) || $adm_data->gender == 'M') echo 'checked'; ?>>
                                <label class="btn gender-btn" for="gender-male">Male</label>
                            </div>

                            <div class="col-md-4" style="padding-left:0px">
                                <input type="radio" class="btn-check" name="gender" id="gender-female" value="F"
                                    <?php if (isset($adm_data->gender) && $adm_data->gender == 'F') echo 'checked'; ?>>
                                <label class="btn gender-btn" for="gender-female">Female</label>
                            </div>

                            <div class="col-md-4" style="padding-left:0px;">
                                <input type="radio" class="btn-check" name="gender" id="gender-other" value="O"
                                    <?php if (isset($adm_data->gender) && $adm_data->gender == 'O') echo 'checked'; ?>>
                                <label class="btn gender-btn" for="gender-other">Others</label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-md-3 col-form-label" style="font-size:15px;">
                        Grade Applied For <span class="text-danger">&nbsp;*</span>
                    </label>
                    <div class="col-md-9">
                        <?php 
                            $array = ['' => 'Select Grade'];
                            foreach ($class_applied_for as $key => $cls) {
                                $friendlyName = isset($class_friendly_name[$cls]) ? $class_friendly_name[$cls]->friendly_name : '';
                                $array[$cls] = $cls . ' ' . $friendlyName;
                            }
                        ?>
                        <select name="class" id="class" class="form-select custom-select-fix" required>
                            <?php foreach ($array as $key => $val) { ?>
                            <option value="<?= $key ?>"
                                <?= (isset($adm_data) && $adm_data->grade_applied_for == $key) ? 'selected' : '' ?>>
                                <?= $val ?>
                            </option>
                            <?php } ?>
                        </select>
                        <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
                        </div>
                    </div>
                </div>


                <!-- Mobile Number -->
                <?php if(!in_array('student_mobile_no',$admission_dispay_fields)) { ?>
                <div class="row mob_num">
                    <label class="col-md-3 col-form-label" style="font-size:15px">Mobile No.
                        <?php if($admission_required_fields['student_mobile_no']['required'] == 'required') {echo '<span class="text-danger">&nbsp *</span>'; } ?></label>
                    <div class="col-md-2 mob_num_country_code">
                        <select name="s_country_code" class="form-control custom-select-fix" id="s_country_code">
                            <?php 
                                    foreach ($this->config->item('country_codes') as $key => $code) { ?>
                            <option value="<?= $code ?>"
                                <?= (isset($adm_data) && $adm_data->s_country_code == $code) ? 'selected' : '' ?>>
                                <?= $code ?></option>
                            <?php } ?>
                        </select>
                        <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
                        </div>
                    </div>
                    <div class="col-md-7 mob_number">
                        <input type="text" class="form-control" placeholder="Enter mobile No." id="student_mobile_no"
                            name="student_mobile_no" data-parsley-pattern="^[0-9 -()+]+$" data-parsley-length="[8, 20]"
                            <?php echo $admission_required_fields['student_mobile_no']['required'] ?>
                            <?php if(isset($adm_data) && !empty($adm_data->student_mobile_no)) { echo 'value="'.$adm_data->student_mobile_no.'"'; } ?>>
                    </div>
                </div>
                <?php } ?>

                <!-- Email Address -->
                <?php if(!in_array('student_email_id',$admission_dispay_fields)) { ?>
                <div class="row">
                    <label class="col-md-3 col-form-label" style="font-size:15px">Email Address
                        <?php if($admission_required_fields['student_email_id']['required'] == 'required') {echo '<span class="text-danger"> &nbsp*</span>'; } ?></label>
                    <div class="col-md-9">
                        <input type="email" class="form-control" placeholder="Enter Email Address" id="student_email_id"
                            name="student_email_id"
                            <?php echo $admission_required_fields['student_email_id']['required'] ?>
                            <?php if(isset($adm_data) && !empty($adm_data->student_email_id)) { echo 'value="'.$adm_data->student_email_id.'"'; } ?>>
                    </div>
                </div>
                <?php } ?>

                <?php if(!in_array('nationality',$admission_dispay_fields)) { ?>
                <div class="row">
                    <label class="col-md-3 col-form-label" style="font-size:15px">Nationality
                        <?php if($admission_required_fields['nationality']['required'] == 'required') {echo '<span class="text-danger">&nbsp *</span>'; } ?></label>
                    <div class="col-md-9">
                        <select name="nationality" class="form-select custom-select-fix" id="nationality"
                            <?php echo $admission_required_fields['nationality']['required'] ?>>
                            <?php 
                                foreach ($this->config->item('nationality') as $key => $nation) { ?>
                            <option value="<?= $nation ?>"
                                <?= (isset($adm_data) && $adm_data->nationality == $nation) ? 'selected' : '' ?>>
                                <?= $nation ?></option>
                            <?php } ?>
                        </select>
                        <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
                        </div>
                    </div>
                </div>
                <?php } ?>

                <?php if(!in_array('s_present_addr',$admission_dispay_fields)) { ?>
                <div class="row">
                    <label class="col-md-3 col-form-label"
                        style="font-size:15px;display:flex;align-items:baseline">Present Address
                        <?php if($admission_required_fields['s_present_addr']['required'] == 'required') {echo '<span class="text-danger"> &nbsp*</span>'; } ?></label>
                    <div class="col-md-9">
                        <input class="form-control" rows="3" style="overflow: hidden; resize: none;"
                            name="s_present_addr" placeholder="Enter the Address"
                            <?php if(isset($adm_data) && !empty($adm_data->s_present_addr)) { echo 'value="'.$adm_data->s_present_addr.'"'; } ?>>
                        <span class="help-block">Write the complete Address
                        </span>

                        <div class="row g-3">
                            <div class="col-md-6 col-12">
                                <input type="text" id="s_present_area" placeholder="Enter Area" class="form-control"
                                    name="s_present_area"
                                    <?php if(in_array('s_present_area',$admission_required_fields)) {echo 'required'; } ?>
                                    <?php if(isset($adm_data) && !empty($adm_data->s_present_area)) { echo 'value="'.$adm_data->s_present_area.'"'; } ?>>
                            </div>
                            <div class="col-md-6 col-12">
                                <input type="text" id="s_present_district" placeholder="Enter District"
                                    class="form-control" name="s_present_district"
                                    data-parsley-error-message="Only alphabets and spaces are allowed."
                                    data-parsley-pattern="^[a-zA-Z ]+$"
                                    <?php if(in_array('s_present_district',$admission_required_fields)) {echo 'required'; } ?>
                                    <?php if(isset($adm_data) && !empty($adm_data->s_present_district)) { echo 'value="'.$adm_data->s_present_district.'"'; } ?>>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12 col-md-4 mb-3 position-relative">
                                <select name="s_present_country" id="s_present_country"
                                    class="form-select custom-select-fix"
                                    <?php if (in_array('s_present_country', $admission_required_fields)) echo 'required'; ?>>
                                    <?php foreach ($this->config->item('country') as $key => $nation) { ?>
                                    <option value="<?= $nation ?>"
                                        <?= (isset($adm_data) && $adm_data->s_present_country == $nation) ? 'selected' : '' ?>>
                                        <?= $nation ?>
                                    </option>
                                    <?php } ?>
                                </select>
                                <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                                    <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
                                </div>
                            </div>

                            <div class="col-12 col-md-4 mb-3 position-relative" id="state_select">
                                <select class="form-select custom-select-fix" id="s_present_state" name="s_present_state">
                                    <option value="">Select State</option>
                                    <?php foreach ($this->config->item('states') as $key => $state) { ?>
                                    <option value="<?= $state ?>"
                                        <?= (isset($adm_data) && $adm_data->s_present_state == $state) ? 'selected' : '' ?>>
                                        <?= $state ?>
                                    </option>
                                    <?php } ?>
                                </select>
                                <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                                    <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
                                </div>
                            </div>

                            <div class="col-12 col-md-4 mb-3" id="state_input" style="display:none;">
                                <input type="text" id="s_present_state1" name="s_present_state1" class="form-control"
                                    placeholder="Enter State" data-parsley-pattern="^[a-zA-Z ]+$"
                                    data-parsley-error-message="Only alphabets and spaces are allowed."
                                    <?php if (isset($adm_data) && !empty($adm_data->s_present_state)) echo 'value="' . $adm_data->s_present_state . '"'; ?>>
                            </div>

                            <div class="col-12 col-md-4 mb-3">
                                <input id="s_present_pincode" name="s_present_pincode" placeholder="Pincode" type="text"
                                    class="form-control" data-parsley-type="digits" data-parsley-length="[3, 10]"
                                    data-parsley-error-message="Enter a valid pin-code, only digits"
                                    <?php if (in_array('s_present_pincode', $admission_required_fields)) echo 'required'; ?>
                                    <?php if (isset($adm_data) && !empty($adm_data->s_present_pincode)) echo 'value="' . $adm_data->s_present_pincode . '"'; ?>>
                            </div>
                        </div>
                    </div>
                </div>
                <?php } ?>

                <?php if(!in_array('boarding',$admission_dispay_fields)) { ?>
                <div class="row">
                    <label class="col-md-3 col-form-label" style="font-size:15px">Boarding Preference
                        <?php if($admission_required_fields['boarding']['required'] == 'required') {echo '<span class="text-danger">&nbsp *</span>'; } ?></label>
                    <div class="col-md-9">
                        <select name="boarding" class="form-control custom-select-fix" id="boarding"
                            <?php echo $admission_required_fields['boarding']['required'] ?>>
                            <?php 
                                $array = array('' => 'Select Preferred Boarding Type');
                                foreach ($this->settings->getSetting('boarding') as $key => $boarding) { ?>
                            <option value="<?= $key ?>"
                                <?= (isset($adm_data) && $adm_data->boarding == $key) ? 'selected' : '' ?>>
                                <?= $boarding ?>
                            </option>
                            <?php } ?>
                        </select>
                        <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
                        </div>
                    </div>
                </div>
                <?php } ?>

                <?php if(!in_array('primary_language_spoken',$admission_dispay_fields)) { ?>
                <div class="row">
                    <label class="col-md-3 col-form-label" style="font-size:15px">Primary Language Spoken
                        <?php if($admission_required_fields['boarding']['required'] == 'required') {echo '<span class="text-danger"> &nbsp*</span>'; } ?></label>
                    <div class="col-md-9">
                        <select name="primary_language_spoken" class="form-control custom-select-fix" id="primary_language_spoken"
                            <?php echo $admission_required_fields['primary_language_spoken']['required'] ?>>
                            <?php 
                                echo '<option value="">Select Preferred Boarding Type</option>';
                                foreach ($this->config->item('languages') as $key => $lan) { ?>
                            <option value="<?= $lan ?>"
                                <?= (isset($adm_data) && $adm_data->primary_language_spoken == $lan) ? 'selected' : '' ?>>
                                <?= $lan ?></option>
                            <?php } ?>
                        </select>
                        <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
                        </div>
                    </div>
                </div>
                <?php } ?>
            </div>
        </div>
        <div class="container" style="margin-top: 3rem;">

            <div class="section-header" style="width: 100%; padding: 0;display: flex; align-items: center; gap: 12px;background: <?php echo !empty($admission_ui_colors['secondary_background_color']) ? $admission_ui_colors['secondary_background_color']: '#EFECFD'?>;color:<?php echo !empty($admission_ui_colors['secondary_font_color']) ? $admission_ui_colors['secondary_font_color'] :  '#000' ?>">
                <span class="section-title">Parents Information</span>
                <span class="section-note small">
                    <span class="text-danger">*</span> Fields mandatory to fill
                </span>
            </div>

            <div class="preview-container">
                <div class="row">
                    <label class="col-md-3 col-form-label" style="font-size:15px">Father Name <span class="text-danger">
                            &nbsp*</span></label>
                    <div class="col-md-6">
                        <input type="text" class="form-control" placeholder="Enter First Name" style="height: 40px;"
                            name="f_name" required="" data-parsley-error-message="Cannot be empty, only alphabets"
                            data-parsley-pattern="^[a-zA-Z. ]+$" data-parsley-minlength="2"
                            <?php if(isset($adm_data) && !empty($adm_data->f_name)) { echo 'value="'.$adm_data->f_name.'"'; } ?>>
                    </div>
                    <?php if(!in_array('f_last_name',$admission_dispay_fields)) { ?>
                    <div class="col-md-3">
                        <input type="text" class="form-control" placeholder="Enter Last Name" name="f_last_name"
                            <?php echo $admission_required_fields['f_last_name']['required'] ?>
                            data-parsley-error-message="Cannot be empty, only alphabets"
                            data-parsley-pattern="^[a-zA-Z. ]+$"
                            <?php if(isset($adm_data) && !empty($adm_data->f_last_name)) { echo 'value="'.$adm_data->f_last_name.'"'; } ?>>
                    </div>
                    <?php }?>
                </div>

                <div class="row">
                    <label class="col-md-3 col-form-label" style="font-size:15px">Mother Name <span
                            class="text-danger">&nbsp *</span></label>
                    <div class="col-md-6">
                        <input type="text" class="form-control" placeholder="Enter First Name" style="height: 40px;"
                            data-parsley-error-message="Cannot be empty, only alphabets"
                            data-parsley-pattern="^[a-zA-Z. ]+$" name="m_name" required
                            <?php if(isset($adm_data) && !empty($adm_data->m_name)) { echo 'value="'.$adm_data->m_name.'"'; } ?>>
                    </div>
                    <?php if(!in_array('m_last_name',$admission_dispay_fields)) { ?>
                    <div class="col-md-3">
                        <input type="text" class="form-control" placeholder="Enter Last Name"
                            <?php echo $admission_required_fields['m_last_name']['required'] ?> name="m_last_name"
                            data-parsley-error-message="Cannot be empty, only alphabets"
                            data-parsley-pattern="^[a-zA-Z. ]+$"
                            <?php if(isset($adm_data) && !empty($adm_data->m_last_name)) { echo 'value="'.$adm_data->m_last_name.'"'; } ?>>
                    </div>
                    <?php }?>
                </div>

                <!-- Grade Selection -->
                <?php if(!in_array('f_mobile_no',$admission_dispay_fields)) { ?>
                <div class="row">
                    <label class="col-md-3 col-form-label" style="font-size:15px">Father Mobile No.
                        <?php if($admission_required_fields['f_mobile_no']['required'] == 'required') {echo '<span class="text-danger"> &nbsp*</span>'; } ?></label>
                    <div class="col-md-2">
                        <select name="f_country_code" class="form-select custom-select-fix" id="f_country_code">
                            <?php 
                                foreach ($this->config->item('country_codes') as $key => $code) { ?>
                            <option value="<?= $code ?>"
                                <?= (isset($adm_data) && $adm_data->f_country_code == $code) ? 'selected' : '' ?>>
                                <?= $code ?></option>
                            <?php } ?>
                        </select>
                        <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
                        </div>
                    </div>
                    <div class="col-md-7">
                        <input type="text" class="form-control" placeholder="Enter mobile No." name="f_mob_num"
                            id="f_mob_num" data-parsley-pattern="^[0-9 -()+]+$" data-parsley-length="[8, 20]"
                            data-parsley-error-message="Enter valid phone number"
                            <?php echo $admission_required_fields['f_mobile_no']['required']?>
                            <?php if(isset($adm_data) && !empty($adm_data->f_mobile_no)) { echo 'value="'.$adm_data->f_mobile_no.'"'; } ?>>
                    </div>
                </div>
                <?php } ?>


                <!-- Mobile Number -->
                <?php if(!in_array('m_mobile_no',$admission_dispay_fields)) { ?>
                <div class="row">
                    <label class="col-md-3 col-form-label" style="font-size:15px">Mother Mobile No.
                        <?php if($admission_required_fields['m_mobile_no']['required'] == 'required') {echo '<span class="text-danger">&nbsp *</span>'; } ?></label>
                    <div class="col-md-2">
                        <select name="m_country_code" class="form-select custom-select-fix" id="m_country_code">
                            <?php 
                                foreach ($this->config->item('country_codes') as $key => $code) { ?>
                            <option value="<?= $code ?>"
                                <?= (isset($adm_data) && $adm_data->m_country_code == $code) ? 'selected' : '' ?>>
                                <?= $code ?></option>
                            <?php } ?>
                        </select>
                        <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
                        </div>
                    </div>
                    <div class="col-md-7">
                        <input type="text" class="form-control" placeholder="Enter mobile No."
                            data-parsley-pattern="^[0-9 -()+]+$" data-parsley-length="[8, 20]"
                            data-parsley-error-message="Enter valid phone number" name="m_mob_num" id="m_mob_num"
                            <?php echo $admission_required_fields['m_mobile_no']['required']?>
                            <?php if(isset($adm_data) && !empty($adm_data->m_mobile_no)) { echo 'value="'.$adm_data->m_mobile_no.'"'; } ?>>
                    </div>
                </div>
                <?php } ?>

                <!-- Email Address -->
                <?php if(!in_array('f_email_id',$admission_dispay_fields)) { ?>
                <div class="row">
                    <label class="col-md-3 col-form-label" style="font-size:15px">Father Email Address
                        <?php if($admission_required_fields['f_email_id']['required'] == 'required') {echo '<span class="text-danger">&nbsp *</span>'; } ?></label>
                    <div class="col-md-9">
                        <input type="email" class="form-control" placeholder="Enter Email Address" name="f_email_id"
                            id="f_email_id" <?php echo $admission_required_fields['f_email_id']['required']?>
                            <?php if(isset($adm_data) && !empty($adm_data->f_email_id)) { echo 'value="'.$adm_data->f_email_id.'"'; } ?>>
                    </div>
                </div>
                <?php } ?>

                <?php if(!in_array('m_email_id',$admission_dispay_fields)) { ?>
                <div class="row">
                    <label class="col-md-3 col-form-label" style="font-size:15px">Mother Email Address
                        <?php if($admission_required_fields['m_email_id']['required'] == 'required') {echo '<span class="text-danger">&nbsp *</span>'; } ?></label>
                    <div class="col-md-9">
                        <input type="email" class="form-control" placeholder="Enter Email Address" name="m_email_id"
                            id="m_email_id" <?php echo $admission_required_fields['m_email_id']['required']?>
                            <?php if(isset($adm_data) && !empty($adm_data->m_email_id)) { echo 'value="'.$adm_data->m_email_id.'"'; } ?>>
                    </div>
                </div>
                <?php } ?>
            </div>
        </div>
        <div class="container" style="margin-top: 3rem;">

            <div class="section-header" style="width: 100%; padding: 0;display: flex; align-items: center; gap: 12px;background: <?php echo !empty($admission_ui_colors['secondary_background_color']) ? $admission_ui_colors['secondary_background_color']: '#EFECFD'?>;color:<?php echo !empty($admission_ui_colors['secondary_font_color']) ? $admission_ui_colors['secondary_font_color'] :  '#000' ?>">
                <span class="section-title">Other Information</span>
                <span class="section-note small">
                    <span class="text-danger">*</span> Fields mandatory to fill
                </span>
            </div>
            <div class="preview-container">
                <?php if(!in_array('know_about_us',$admission_dispay_fields)) { ?>
                <div class="row">
                    <label class="col-md-3 col-form-label" style="font-size:15px">Referral Source
                        <?php if($admission_required_fields['know_about_us']['required'] == 'required') {echo '<span class="text-danger">&nbsp *</span>'; } ?></label>
                    <div class="col-md-9">
                        <select name="know_about_us" id="know_about_us" class="form-select custom-select-fix"
                            <?php echo $admission_required_fields['know_about_us']['required'] ?>>
                            <option value="" selected disabled hidden>Select</option>
                            <option value="Newspaper Advertisement"
                                <?= (isset($adm_data) && $adm_data->know_about_us == 'Newspaper Advertisement') ? 'selected' : '' ?>>
                                Newspaper Advertisement</option>
                            <option value="Google Search Online Ads"
                                <?= (isset($adm_data) && $adm_data->know_about_us == 'Google Search Online Ads') ? 'selected' : '' ?>>
                                Google Search Online Ads</option>
                            <option value="Friends / Family"
                                <?= (isset($adm_data) && $adm_data->know_about_us == 'Friends / Family') ? 'selected' : '' ?>>
                                Friends / Family</option>
                            <option value="Social Media"
                                <?= (isset($adm_data) && $adm_data->know_about_us == 'Social Media') ? 'selected' : '' ?>>
                                Social Media</option>
                            <option value="Fair"
                                <?= (isset($adm_data) && $adm_data->know_about_us == 'Fair') ? 'selected' : '' ?>>
                                Fair</option>
                            <option value="Old Harrovian"
                                <?= (isset($adm_data) && $adm_data->know_about_us == 'Old Harrovian') ? 'selected' : '' ?>>
                                Old Harrovian</option>
                            <option value="YPO Communication"
                                <?= (isset($adm_data) && $adm_data->know_about_us == 'YPO Communication') ? 'selected' : '' ?>>
                                YPO Communication</option>
                            <option value="Magazine"
                                <?= (isset($adm_data) && $adm_data->know_about_us == 'Magazine') ? 'selected' : '' ?>>
                                Magazine</option>
                            <option value="Others"
                                <?= (isset($adm_data) && $adm_data->know_about_us == 'Others') ? 'selected' : '' ?>>
                                Others</option>
                        </select>
                        <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
                        </div>
                    </div>
                </div>
                <?php } ?>
            </div>
        </div>
        <?php if ($this->mobile_detect->isTablet() ) { ?>
        <div style="display: flex; gap: 10px; justify-content: center; align-items: center; padding: 20px 0">
            <a class="btn cancel-btn"
                style="border: 1px solid #6B7280; background: transparent; color: #374151; padding: 13px 16px; border-radius: 6px; text-align: center; font-size: 16px"
                href="<?php echo site_url('admissions/home') ?>">Cancel</a>
            <button class="btn" id="paybtn"
                style="background: <?php echo ! empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] :  '#623CE7' ?>; color: <?php echo !empty($admission_ui_colors['primary_font_color']) ? $admission_ui_colors['primary_font_color'] :  'white' ?>; border: none; padding: 13px 16px; border-radius: 6px; font-size: 16px">Save
                & Proceed</button>
        </div>
        <?php }else if($this->mobile_detect->isMobile()) { ?>
        <div
            style="display: flex; flex-direction: column; gap: 10px; padding: 20px 24px; justify-content: center; align-items: center;">

            <button class="btn" id="paybtn"
                style="background: <?php echo ! empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] :  '#623CE7' ?>; color: <?php echo !empty($admission_ui_colors['primary_font_color']) ? $admission_ui_colors['primary_font_color'] :  'white' ?>; border: none; width:100%;padding: 13px 24px; border-radius: 10px;">
                Save & Proceed
            </button>

            <a class="btn cancel-btn"
                style="border: 1px solid #6B7280; background: transparent; color: #374151; width:100% ;padding: 13px 24px;border-radius: 10px; text-align: center;"
                href="<?php echo site_url('admissions/home') ?>">
                Cancel
            </a>
        </div>

        <?php } else { ?>
        <div style="gap: 10px; padding: 20px 20px;float:right;">
            <a class="btn cancel-btn"
                style="border: 1px solid #6B7280; background: transparent; color: #374151; padding: 13px 16px; border-radius: 8px; text-align: center; font-size: 16px;margin-right:5px"
                href="<?php echo site_url('admissions/home') ?>">Cancel</a>
            <button class="btn" id="paybtn"
                style="background: <?php echo ! empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] :  '#623CE7' ?>; color: <?php echo !empty($admission_ui_colors['primary_font_color']) ? $admission_ui_colors['primary_font_color'] :  'white' ?>; border: none; padding: 13px 16px; border-radius: 8px; font-size: 16px">Save
                & Proceed</button>
        </div>
        <?php } ?>
    </form>
</div>


<script>
$(document).ready(function() {
    var af_id = '<?php echo $af_id ?>';
    console.log(af_id);
    get_instructions('<?php echo $admission_setting_id ?>',af_id);
    $('form').parsley();
    $('#student_dob').on('change', function() {
        $(this).parsley().validate();
    });
});

function get_instructions(adm_setting_id,af_id='') {
    // $("body").addClass("modal1");
    // $(".modal1").css("display", 'contents');
    if(af_id != '' && af_id != '0'){
        return false;
    }
    var id = 'watermark';
    var panelId = 'contentId';
    var srcpath = '<?php echo $this->settings->getSetting('school_logo') ?>';
    var short_name = '<?php echo $this->settings->getSetting('school_short_name') ?>';
    if (short_name == 'sahitya') {
        id = 'watermark1';
        srcpath = '';
        panelId = '';
    }
    var src = '<?php echo base_url() ?>' + srcpath;
    $.ajax({
        url: '<?php echo site_url('Admission_user/instruction_by_setting_id'); ?>',
        type: 'post',
        data: {
            'admission_setting_id': adm_setting_id
        },
        success: function(data) {
            var instruction_obj = $.parseJSON(data);
            var html = '';
            html += `${instruction_obj.guidelines}`;
            html += `${instruction_obj.address.line1}`;
            html += `${instruction_obj.address.line2}`;
            html += `${instruction_obj.address.line3}`;
            html += `${instruction_obj.address.line4}`;
            html += `${instruction_obj.address.phone}`;
            html += `${instruction_obj.address.fax}`;
            html += `${instruction_obj.address.email}`;

            Swal.fire({
                title: `
        <div class="swal2-header-custom">
            <span>Instructions</span>
        </div>
    `,
                html: html,
                width: '700px',
                showCloseButton: false,
                confirmButtonText: 'Close',
                confirmButtonColor: '#623CE7',
                customClass: {
                    popup: 'swal2-rounded-popup',
                    confirmButton: 'swal2-rounded-button',
                }
            });
        }
    });
}

$("#s_present_country").change(function() {
    // var present_state_required = '<?php //echo $admission_required_fields['s_present_state']['required'] ?>';
    var country = $('#s_present_country').val();
    if (country == 'India') {
        $('#state_select').show();
        $('input[type=text][name=s_present_state1]').removeAttr("required");
        $('#state_input').hide();
        // if(present_state_required){
        //     $('select[name=s_present_state]').attr('required','required');
        // }
    } else {
        $('#state_input').show();
        $('select[name=s_present_state]').removeAttr("required");
        $('#state_select').hide();
        // if(present_state_required){
        //     $('input[type=text][name=s_present_state1]').attr('required','required');
        // }
    }
});
</script>



<style>
.swal2-header-custom,
.swal2-html-container {
    text-align: left
}

.file-upload-box {
    border: 2px dashed #ccc;
    padding: 30px;
    text-align: center;
    cursor: pointer;
    background-color: #f9f9f9;
    border-radius: 5px;
}

.file-upload-box:hover {
    background-color: #f1f1f1;
}

.info-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    font-size: 14px;
    font-weight: bold;
    color: #003399;
    border: 1px solid #003399;
    cursor: pointer;
    text-align: center;
}

.form-control {
    /* height: 55px !important; */
    /* Forces height */
    width: 100%;
    padding: 10px;
    font-size: 16px;
    border: 1px solid #EAEAEA;
    border-radius: 10px !important;
}

.form-select {
    border: 1px solid #EAEAEA;
    width: 100%;
    padding: 10px;
    border-radius: 10px !important;
}

label {
    color: var(--Blac, #212121);
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 120%;
}

.col-form-label {
    text-align: left !important;
    display: flex;
    align-items: center;
}

input,
select {
    font-size: 16px !important;
    border-radius: 4px !important;
    background-color: #fff !important;
    color: #000 !important;
    height: auto !important;
    line-height: normal !important;
    padding: 19px 24px !important;
}

select {
    appearance: auto !important;
    min-height: 45px;
}

.row {
    padding: 10px 0px !important;
}

@media screen and (min-width: 992px) {
    .row {
        display: flex;
    }
}

.swal2-rounded-popup {
    border-radius: 24px;
    padding: 0;
    /* remove default padding to allow full-width header */
    text-align: left;
    overflow: hidden;
    /* ensures header corners are rounded */
    font-family: 'Segoe UI', sans-serif;
    font-size: 14px;
}

.swal2-title {
    background-color: #EDE9FE;
    /* light purple */
    padding: 0 24px;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    border-top-left-radius: 24px;
    border-top-right-radius: 24px;
    /* display: flex;
    justify-content: space-between;
    align-items: center; */
}

.swal2-rounded-button {
    border-radius: 8px !important;
    font-weight: 500;
    padding: 10px 13px;
    width: 127px;
    margin: 16px auto 24px auto;
    display: block;
}

.swal2-close-right {
    position: absolute !important;
    right: 16px;
    top: 16px;
    font-size: 18px !important;
    color: #555;
}

.btn-check {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

/* Style the visible label buttons */

.gender-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    /* ensure full vertical height */
    padding: 18px 24px;
    border: 1px solid #EAEAEA;
    border-radius: 8px;
    background-color: #fff;
    cursor: pointer;
    font-weight: 400;
    text-align: center;
    color: #000;
    transition: all 0.3s ease;
    font-size: 16px;

}

/* Style the selected (active) gender button */
.btn-check:checked+.gender-btn {
    background-color: #6C3BFF;
    color: #fff;
    border-color: #6C3BFF;
}

.custom-select-fix {
    appearance: none !important;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: none;
    padding-right: 20px !important;
}

@media screen and (max-width: 768px) {
    .section-header {
        flex-wrap: wrap;
        /* Allow wrapping of elements */
        justify-content: space-between;
        /* Align items properly */
        align-items: center;
        /* Center align items vertically */
        padding: 1rem;
        /* Add padding for spacing */
        margin-bottom: 1rem;
        /* Add margin below the header */
        gap: 0.5rem;
    }

    .section-title {
        font-size: 16px;
        /* Adjust font size for smaller screens */
        margin-bottom: 0;
        /* Remove extra margin */
        flex: 1;
        /* Allow title to take up available space */
        font-weight: 500;
    }

    .section-note {
        font-size: 14px;
        /* Adjust font size for smaller screens */
        margin-bottom: 0;
        /* Remove extra margin */
        flex-shrink: 0;
        /* Prevent shrinking of the note */
    }

    .row {
        flex-direction: column;
        padding: 10px 0 !important;
    }

    .col-md-3,
    .col-md-6,
    .col-md-9,
    .col-md-2,
    .col-md-7 {
        width: 100%;
        padding: 0;
        margin-bottom: 1rem;
    }

    .form-control,
    .form-select {
        font-size: 14px;
        padding: 8px;
    }

    .preview-container {
        box-shadow: none;
        border-radius: 8px;
    }

    .gender_btn,
    .mob_num {
        display: ruby;
    }

    .mob_num_country_code {
        width: 30% !important;
    }

    .mob_number {
        width: 69% !important;
    }
}

@media screen and (max-width: 992px) {
    .section-header {
        width: 100%;
        height: 70px;
        /* light violet */
        border-radius: 24px 24px 0 0;
        padding: 0 1rem;
    }

    .section-title {
        font-size: 16px;
        /* Slightly larger font size for tablets */
        font-weight: 500;
    }

    .section-note {
        font-size: 12px;
        /* Adjust font size for tablets */
    }

    .col-md-3,
    .col-md-6,
    .col-md-9,
    .col-md-2,
    .col-md-7 {
        width: 100%;
        margin-bottom: 1rem;
    }

    .form-control,
    .form-select {
        font-size: 15px;
        padding: 10px;
    }

    .preview-container {
        padding: 1.5rem;
        box-shadow: none;
        border-radius: 10px;
    }
}
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>