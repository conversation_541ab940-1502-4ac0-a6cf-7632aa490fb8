<style>
.preview-container {
    max-width: 900px;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    padding: 40px;
}

.profile-section img {
    border-radius: 6px;
    width: 124px;
    /* height: 156px; */
    object-fit: cover;
}

.profile-info h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.profile-info p {
    margin: 4px 0;
    color: #6b7280;
}

.info-block {
    margin-top: 30px;
    border-top: 1px solid #e5e7eb;

}

.info-block h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 20px 0;
    padding-bottom: 10px;
    font-style: normal;
    color: #101010;
}

.info-grid div {
    margin-bottom: 10px;
}

.label {
    font-size: 14px;
    color: hsl(218, 10.60%, 64.90%);
    padding: 0;
    font-style: normal;
    font-weight: 400;
    color: #A3ABC0;
    align-self: stretch;
    margin-bottom: 5px;
}

.value {
    color: #101010;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 120%;

}

.section-header {
    width: 100%;
    height: 70px;
    background: #EFECFD;
    border-radius: 24px 24px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 15px;
}

.info-grid>div {
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
    /* space between items */
}

.info-grid .label {
    font-weight: 400;
    margin-bottom: 5px;
    /* space between label and value */
    text-align: left;
}

.info-grid .value {
    color: #444;
}
</style>
<?php 
$admission_ui_colors = [];
$ui_colors_array = $this->settings->getSetting('admissions_ui_theme_color');

if (!empty($ui_colors_array)) {
    $admission_ui_colors = array_column($ui_colors_array, 'value', 'name');
} ?>
<div class="" style="background-color:#F9F7FE;font-style: normal;">
    <form enctype="multipart/form-data" id="final-form" class="form-horizontal" data-parsley-validate method="post"
        action="<?php echo site_url('admission_controller/make_partial_payment') ?>">
        <input type="hidden" id="auId" name="auId" value="<?= $au_id; ?>">
        <input type="hidden" id="lastId" name="lastId" value="<?= $lastId; ?>">
        <input type="hidden" id="admission_setting_id" name="admission_setting_id"
            value="<?= $admission_setting_id; ?>">


        <div style="background-color: #F9F7FE; padding: 20px 22px;">
            <div style="display: flex; align-items: center; gap: 12px;">
                <!-- Back Icon -->
                <a href="<?php echo site_url('admissions/home') ?>" style="display: flex; align-items: center;">
                    <div style="width: 16px; height: 16px;">
                        <?php $this->load->view('svg_icons/back_icon.svg') ?>
                    </div>
                </a>

                <!-- Title -->
                <span style="font-weight: 600; font-size: 16px; color: #111;">
                    Application Details - <?= $config_val['form_name'].' '.$config_val['form_year'] ?>
                </span>

            </div>
        </div>

        <div class="container">
            <div class="section-header d-flex justify-content-between align-items-center" style="width: 100%;">
                <span class="section-title fw-semibold">Application Preview</span>
            </div>
            <div class="preview-container">


                <div class="profile-section">
                    <img src="https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/Admission process/student_image.jpg"
                        alt="Profile Picture">
                    <div class="profile-info">
                        <h2><?= ucfirst($admission_data->student_name) ?></h2>
                        <p style="color: #0A0D14;">Grade <?= $admission_data->grade_applied_for ?></p>
                        <p style="color: #0A0D14;">DOB: <?= $admission_data->dob ?></p>
                    </div>
                </div>

                <div class="info-block">
                    <h3>Personal Information</h3>
                    <div class="info-grid">
                        <div><span class="label">First Name</span><br><span
                                class="value"><?= ucfirst($admission_data->std_name) ?></span></div>
                        <div><span class="label">Last Name</span><br><span
                                class="value"><?= ($admission_data->student_last_name) ? ucfirst($admission_data->student_last_name) : '-' ?></span>
                        </div>
                        <div><span class="label">Date of Birth</span><br><span
                                class="value"><?= $admission_data->dob ?></span>
                        </div>
                        <div><span class="label">Gender</span><br><span
                                class="value"><?= $admission_data->gender ?></span>
                        </div>
                        <?php if(!in_array('nationality',$admission_dispay_fields)) { ?>
                        <div><span class="label">Nationality</span><br><span
                                class="value"><?= ($admission_data->nationality) ? $admission_data->nationality : '-' ?></span>
                        </div>
                        <?php } ?>
                        <?php if(!in_array('student_email_id',$admission_dispay_fields)) { ?>
                        <div><span class="label">Email Address</span><br><span
                                class="value"><?= ($admission_data->student_email_id) ? $admission_data->student_email_id : '-' ?></span>
                        </div>
                        <?php } ?>
                        <?php if(!in_array('student_mobile_no',$admission_dispay_fields)) { ?>
                        <div><span class="label">Mobile No.</span><br><span
                                class="value"><?= ($admission_data->student_mobile_no) ? $admission_data->s_country_code.' '.$admission_data->student_mobile_no : '-' ?></span>
                        </div>
                        <?php } ?>
                        <?php if(!in_array('boarding',$admission_dispay_fields)) { ?>
                        <div><span class="label">Boarding</span><br><span
                                class="value"><?= ($admission_data->boarding) ? $admission_data->boarding : '-' ?></span>
                        </div>
                        <?php } ?>

                        <?php if(!in_array('primary_language_spoken',$admission_dispay_fields)) { ?>
                        <div><span class="label">Primary Language</span><br><span
                                class="value"><?= ($admission_data->primary_language_spoken) ? $admission_data->primary_language_spoken : '-' ?></span>
                        </div>
                        <?php } ?>

                        <?php if(!in_array('s_present_addr',$admission_dispay_fields)) {  ?>
                        <?php $student_address = ''; $s_present_addr = ''; $s_present_area = ''; $s_present_district = ''; $s_present_state = ''; $s_present_country='';$s_present_pincode = '';
                            $s_present_addr = !empty($admission_data->s_present_addr) ?   $admission_data->s_present_addr : $s_present_addr;
                            $s_present_area = !empty($admission_data->s_present_area) ? $admission_data->s_present_area : '';
                            $s_present_district = !empty($admission_data->s_present_district) ? $admission_data->s_present_district : '';
                            $s_present_state = !empty($admission_data->s_present_state) ? $admission_data->s_present_state : '';
                            $s_present_country = !empty($admission_data->s_present_country) ? $admission_data->s_present_country : '';
                            $s_present_pincode = !empty($admission_data->s_present_pincode) ? $admission_data->s_present_pincode : '';

                            $s_address_parts = [];

                            if ($s_present_addr) $s_address_parts[] = $s_present_addr;
                            if ($s_present_area) $s_address_parts[] = $s_present_area;
                            if ($s_present_district) $s_address_parts[] = $s_present_district;
                            if ($s_present_state) $s_address_parts[] = $s_present_state;
                            if ($s_present_country) $s_address_parts[] = $s_present_country;

                            $student_address = implode(', ', $s_address_parts);

                            if ($s_present_pincode) {
                                $student_address .= ' - ' . $s_present_pincode;
                            }
                        ?>
                        <div><span class="label">Present Address</span><br><span
                                class="value"><?= ($student_address) ? $student_address : '-' ?></span>
                        </div>
                        <?php } ?>
                    </div>
                </div>

                <div class="info-block">
                    <h3>Parents Information</h3>
                    <div class="info-grid">
                        <div><span class="label">Father Name</span><br><span
                                class="value"><?= ($admission_data->f_name || $admission_data->f_last_name) ? $admission_data->f_name.' '.$admission_data->f_last_name : '-' ?></span>
                        </div>
                        <div><span class="label">Mother Name</span><br><span
                                class="value"><?= ($admission_data->m_name || $admission_data->m_last_name) ? $admission_data->m_name.' '.$admission_data->m_last_name : '-' ?></span>
                        </div>

                        <?php if(!in_array('f_email_id',$admission_dispay_fields)) { ?>
                        <div><span class="label">Father Email Address</span><br><span
                                class="value"><?= ($admission_data->f_email_id) ? $admission_data->f_email_id : '-' ?></span>
                        </div>
                        <?php } ?>

                        <?php if(!in_array('m_email_id',$admission_dispay_fields)) { ?>
                        <div><span class="label">Mother Email Address</span><br><span
                                class="value"><?= ($admission_data->m_email_id) ? $admission_data->m_email_id : '-' ?></span>
                        </div>
                        <?php } ?>

                        <?php if(!in_array('f_mobile_no',$admission_dispay_fields)) { ?>
                        <div><span class="label">Father Mobile No.</span><br><span
                                class="value"><?= ($admission_data->f_mobile_no) ? $admission_data->f_country_code.' '.$admission_data->f_mobile_no : '-'; ?></span>
                        </div>
                        <?php } ?>

                        <?php if(!in_array('m_mobile_no',$admission_dispay_fields)) { ?>
                        <div><span class="label">Mother Mobile No.</span><br><span
                                class="value"><?= ($admission_data->m_mobile_no) ? $admission_data->m_country_code.' '.$admission_data->m_mobile_no : '-'; ?></span>
                        </div>
                        <?php } ?>


                    </div>
                </div>
                <div class="info-block">
                    <h3>Other Information</h3>
                    <?php if(!in_array('know_about_us',$admission_dispay_fields)) { ?>
                    <div><span class="label">Where You Heard About Us?</span><br><br><span
                            class="value"><?= ($admission_data->know_about_us) ? $admission_data->know_about_us : '-'; ?></span>
                    </div>
                    <?php } ?>
                </div>
            </div>

        </div>
        <?php if ($this->mobile_detect->isTablet()) { ?>
        <div style="display: flex; justify-content: center;gap: 10px;margin:1rem 0">
            <a class="btn"
                style="border: 1px solid #6B7280; background: transparent; color: #374151; padding: 13px 16px; border-radius: 6px;font-size:14px"
                onclick="edit_application()">Edit Application</a>
            <a class="btn"
                style="border: 1px solid #6B7280; background: transparent; color: #374151; padding: 13px 16px; border-radius: 6px;font-size:14px"
                href="<?php echo site_url('admissions/home') ?>">Submit Later</a>
            <button class="btn" id="paybtn"
                style="background: <?php echo ! empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] :  '#623CE7' ?>; color: <?php echo !empty($admission_ui_colors['primary_font_color']) ? $admission_ui_colors['primary_font_color'] :  'white' ?>; border: none; padding: 13px 16px; border-radius: 6px;font-size:14px">Save
                & Pay</button>
            <!-- <button class="btn btn-info" id="paybtn" style="border: none; padding: 10px 20px; font-size: 16px;">Pay and Submit</button> -->
        </div>
        <?php } else if ($this->mobile_detect->isMobile()) { ?>
        <div
            style="display: flex; flex-direction: column; gap: 10px;justify-content: center;margin:1rem 0;padding:10px 20px">
            <a class="btn"
                style="border: 1px solid #6B7280; background: transparent; color: #374151; padding: 13px 16px; border-radius: 6px;font-size:14px"
                onclick="edit_application()">Edit Application</a>
            <a class="btn"
                style="border: 1px solid #6B7280; background: transparent; color: #374151; padding: 13px 16px; border-radius: 6px;font-size:14px"
                href="<?php echo site_url('admissions/home') ?>">Submit Later</a>
            <button class="btn" id="paybtn"
                style="background: <?php echo ! empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] :  '#623CE7' ?>; color: <?php echo !empty($admission_ui_colors['primary_font_color']) ? $admission_ui_colors['primary_font_color'] :  'white' ?>; border: none; padding: 13px 16px; border-radius: 6px;font-size:14px">Save
                & Pay</button>
        </div>
        <?php } else { ?>
        <div style="display: flex; gap: 10px;justify-content: center;margin:1rem 0">
            <a class="btn"
                style="border: 1px solid #6B7280; background: transparent; color: #374151; padding: 13px 16px; border-radius: 6px;font-size:14px"
                onclick="edit_application()">Edit Application</a>
            <a class="btn"
                style="border: 1px solid #6B7280; background: transparent; color: #374151; padding: 13px 16px; border-radius: 6px;font-size:14px"
                href="<?php echo site_url('admissions/home') ?>">Submit Later</a>
            <button class="btn" id="paybtn"
                style="background: <?php echo ! empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] :  '#623CE7' ?>; color: <?php echo !empty($admission_ui_colors['primary_font_color']) ? $admission_ui_colors['primary_font_color'] :  'white' ?>; border: none; padding: 13px 16px; border-radius: 6px;font-size:14px">Save
                & Pay</button>
        </div>
        <?php } ?>
    </form>
</div>


<script>
function edit_application() {
    url = '<?php echo site_url('admissions/start_application') ?>';
    $('#final-form').attr('action', url);
    $('#final-form').submit();
}
</script>

<?php if($this->mobile_detect->isMobile() && !$this->mobile_detect->isTablet()) { ?>

<style>
.profile-info {
    margin-top: 15px;
}

.container {
    /* max-width: 100%; */
    width: revert;
    margin: 0 2rem;
    background-color: #fff;
    border-radius: 24px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    padding: 0;
}

.school_name_in_header {
    font-size: 15px !important;
}

.section-title {
    font-weight: 600;
    color: #000;
    margin-left: 32px;
    font-size: 16px;
    font-style: normal;
}
</style>

<?php } else { ?>
<style>
.info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px 40px;
}

.profile-section {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header {
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 20px;
    margin-bottom: 30px;
}

.container {
    /* max-width: 100%; */
    width: revert;
    margin: 0 2rem;
    background-color: #fff;
    border-radius: 24px;
    padding: 0;
}

.section-title {
    font-weight: 600;
    color: #000;
    margin-left: 32px;
    font-size: 18px;
    font-style: normal;
}
</style>

<?php } ?>