<?php 
$admission_ui_colors = [];
$ui_colors_array = $this->settings->getSetting('admissions_ui_theme_color');

if (!empty($ui_colors_array)) {
    $admission_ui_colors = array_column($ui_colors_array, 'value', 'name');
}
?>

<?php if (!empty($my_application)) { ?>
<div class="container" style="background-color: #F9F7FE; position: relative;margin-bottom:0;padding-bottom:0">
    <div class="row">
        <div class="col-md-12" style="margin: 2rem 0">
            <?php if ($admission_form_word) { ?>
            <h3 class="panel-title" style="margin-left:10px;font-size: 20px;font-weight:500">
                <?= $admission_form_word ?>
            </h3>
            <?php } else { ?>
            <h3 class="panel-title" style="margin-left:10px;font-size: 20px; font-weight:500">
                Applications
            </h3>
            <?php } ?>
        </div>
        <?php if (!empty($my_application)) { ?>
        <?php foreach ($my_application as $key => $app) { ?>
        <?php 
                    $card_top_color = $app->curr_status == 'Draft' || $app->curr_status == 'Payment Pending' ? '#FFECD2' : '#D5FDE7';
                    $card_text_color = $app->curr_status == 'Draft' || $app->curr_status == 'Payment Pending' ? '#952E23' : '#077D36';
                    $border_color = $app->curr_status == 'Draft' || $app->curr_status == 'Payment Pending' ? '#FEDFB3' : '#9DFDC8';
                    if ($app->curr_status == 'Payment Pending') {
                        $card_status = 'Payment Pending';
                    } elseif ($app->enable_partial_payment == 1 && $app->fee_paid_status == 0 && $app->curr_status == 'Draft') {
                        $card_status = 'Payment Pending';
                    } elseif ($app->curr_status == 'Draft') {
                        $card_status = 'In-complete Application';
                    } else {
                        $card_status = 'Application Submitted';
                    }
                ?>

        <div class="col-12 col-sm-6 col-md-3" style="margin-bottom: 20px;">
            <div
                style="background: #fff; border-radius: 24px; overflow: hidden; text-align: center; border: 1px solid #eee;">

                <!-- Card Top Section -->
                <div
                    style="background: <?= $card_top_color ?>; color: <?= $card_text_color ?>; font-weight: 500; font-size: 16px; padding: 10px 5px 5px 5px;border-radius:24px;">
                    <div
                        style="display: flex; align-items: center; justify-content: center; gap: 8px; font-size: 14px; margin-bottom: 10px; font-weight: 500; font-style: normal; letter-spacing: 0.7px; line-height: 1.5;padding:3px 0">
                        <?= $card_status ?>
                    </div>
                    <div style="background: #fff;border-radius: 19px;border: 1.4px solid <?= $border_color ?>;">


                        <div style="padding: 13px 24px;">
                            <div style="padding: 0 0 13px 0">
                                <span class="text-muted mb-2"
                                    style="font-size: 13px; font-style: normal; font-weight: 400; color: #818181 !important;letter-spacing: 0.5px; line-height: 1.5;">Application
                                    Number</span>
                                <div class="fw-bold"
                                    style="color: #212121; font-size: 16px; font-style: normal; font-weight: 500;letter-spacing: 0.5px; line-height: 1.5;">
                                    <?= empty($app->application_no) ? 'Not Generated' : $app->application_no ?>
                                </div>
                            </div>

                            <?php if ($app->std_photo_uri) { ?>
                                <img src="<?= $this->filemanager->getFilePath($app->std_photo_uri); ?>" alt="Student Photo"
                                    style="width: 100%; max-width: 100%; height: 184px; border-radius: 12px; object-fit: cover; background: #f0f0f0;">
                            <?php } else { ?>
                                <div style="width: 100%; max-width: 100%; height: 184px; border-radius: 12px; object-fit: cover; background: #f0f0f0; display: flex; align-items: center; justify-content: center;">
                                    <?php $this->load->view('svg_icons/empty_image.svg'); ?>
                                </div>
                            <?php } ?>
                            
                        </div>

                        <!-- Student Name -->
                        <div style="padding: 0 20px;margin-top:0px">
                            <span style="color: #212121;font-size: 16px;font-style: normal;font-weight: 500;letter-spacing: 0.5px; line-height: 1.5;">
                                <?php if(!empty($app->std_name)) { ?>
                                <?= ucfirst(strtolower($app->std_name)) . ' ' . ucfirst(strtolower($app->student_last_name)) ?>
                                </h4>
                                <?php } else { ?>
                                Not Specified
                                <?php } ?>
                                </span>
                        </div>

                        <!-- Grade / Application Info -->
                        <div style="padding: 0px 20px; color: #818181;margin-bottom:16px;letter-spacing: 0.5px; line-height: 1.5;">

                            <p style=" font-size: 14px;font-weight: 400;">Applied for:
                                <?= !empty($app->grade_applied_for) ? $app->grade_applied_for : 'Not Specified' ?></p>
                            <!-- <p><small>Status:</small> 
                                //($app->curr_status == 'Draft') ? 'Not Submitted' : $app->curr_status . ' ('. date('d-m-Y', strtotime($app->status_changed_on)) . ')' 
                            </p> -->
                            <?php if($app->revert_info != '0'){ ?>
                            <p>
                                <small>Reverted on:</small> <?= date('d-m-Y', strtotime($app->status_changed_on)) ?>
                                <br>
                                <a class="btn btn-sm btn-warning mt-2" data-toggle="modal"
                                    data-rever_info="<?= $app->revert_info ?>" data-target="#revert_reason">Revert
                                    Reason</a>
                            </p>
                            <?php } ?>
                        </div>

                        <!-- Button Area -->
                        <div style="padding:0px 24px 20px 24px">

                            <?php if ($app->enable_partial_payment == 1 && ($app->curr_status == 'Draft'|| $app->curr_status == 'Payment Pending')): ?>
                            <form enctype="multipart/form-data" id="form_id_<?= $app->id ?>"
                                action="<?= site_url('admissions/new') ?>" class="form-horizontal" data-parsley-validate
                                method="post">
                                <input type="hidden" name="admission_setting_id"
                                    value="<?= $app->admission_setting_id ?>">
                                <input type="hidden" name="lastId" value="<?= $app->id ?>">
                                <input type="hidden" name="au_id" value="<?= $au_id ?>">
                                <?php $disabled = ($app->enable_partial_payment == 1 && $app->fee_paid_status != 1) ? 'disabled' : ''; ?>

                                <?php if ($app->fee_paid_status == 1): ?>
                                <center><button type="submit" class="continue-button" <?= $app->continue_status ?>
                                        <?= $disabled ?>>Continue →</button></center>
                                <?php else: ?>
                                <button type="button" class="continue-button"
                                    onclick="preview_minimum_fields(<?= $app->id ?>, <?= $au_id ?>, <?= $app->admission_setting_id ?>)">Pay
                                    Application Amount →</button>
                                <?php endif; ?>
                            </form>

                            <?php elseif ($app->curr_status == 'Draft' || $app->curr_status == 'Payment Pending'): ?>
                            <form enctype="multipart/form-data" id="form_id" action="<?= site_url('admissions/new') ?>"
                                class="form-horizontal" data-parsley-validate method="post">
                                <input type="hidden" name="admission_setting_id"
                                    value="<?= $app->admission_setting_id ?>">
                                <input type="hidden" name="lastId" value="<?= $app->id ?>">
                                <input type="hidden" name="au_id" value="<?= $au_id ?>">
                                <?php $disabled = ($app->enable_partial_payment == 1 && $app->fee_paid_status != 1) ? 'disabled' : ''; ?>
                                <center><button type="submit" class="continue-button" <?= $app->continue_status ?>
                                        <?= $disabled ?>>Continue →</button></center>
                            </form>

                            <?php else:
                                $onPay = ($app->online_payment == 1);
                                if ($onPay) {
                                    $url = ($app->payment_status === 'SUCCESS') ? site_url('admissions/print') : site_url('admissions/preview');
                                    $recipet_url = site_url('admissions/print_receipt/' . $app->id);
                                } else {
                                    $url = site_url('admissions/print');
                                    $recipet_url = site_url('admissions/print_receipt/' . $app->id);
                                }
                            ?>
                            <form enctype="multipart/form-data" id="my-form" class="form-horizontal"
                                action="<?= $url ?>" data-parsley-validate method="post">
                                <input type="hidden" name="admission_setting_id"
                                    value="<?= $app->admission_setting_id ?>">
                                <input type="hidden" name="lastId" value="<?= $app->id ?>">

                                <?php if ($onPay && $app->payment_status === 'SUCCESS'): ?>
                                <button type="submit" class="continue-button"
                                    <?= empty($app->status) ? 'disabled' : '' ?>>View Application →</button>
                                <?php elseif ($onPay): ?>
                                <button type="submit" class="continue-button">Payment Pending →</button>
                                <?php else: ?>
                                <button type="submit" class="continue-button"
                                    <?= empty($app->status) ? 'disabled' : '' ?>>View Application →</button>
                                <?php endif; ?>

                                <?php if (!empty($app->rejected_documents) && $this->settings->getSetting('document_verification_in_admissions') == 1): ?>
                                <button type="button" class="continue-button" data-toggle="modal"
                                    data-target="#rejected_documents_modal"
                                    onclick="get_rejected_documents('<?= $app->id ?>','<?= $app->admission_setting_id ?>')"
                                    style="float:right;">Rejected Documents</button>
                                <?php endif; ?>
                            </form>
                            <?php endif; ?>

                        </div>
                    </div>
                </div>


                <!-- Profile Image -->

            </div>
        </div>
        <?php } ?>
        <?php } ?>
    </div>
</div>
<?php } ?>

<style>
.continue-button {
    /* background: #623CE7; */
    color: <?php echo !empty($admission_ui_colors['primary_font_color']) ? $admission_ui_colors['primary_font_color'] :  'white' ?>;
    border: none;
    padding: 13px 24px;
    font-size: 16px;
    font-weight: 400;
    width: 100%;
    border-radius: 8px;
    cursor: pointer;
    margin-top: 10px;
    text-align: center;
    display: block;
    transition: background 0.3s;
    background: <?php echo ! empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] :  '#623CE7' ?>;
}
</style>

<br>

<?php if($this->settings->getSetting('disable_new_admissions_tab') == 0 && !empty($admissions)) { ?>
    <div class="container" style="background-color: #F9F7FE;margin-top:0;padding-top:0">
    <div class="col-md-12" style="margin: 10px 0">
    <h3 style="color:black;margin-left:10px;font-weight:500">New Applications</h3>
    </div>
    <div class="admission-grid" style="margin-top: 40px;">
        <?php $i = 0; foreach ($admissions as $key => $name) { ?>
        <?php if($name['open_for_admissions'] == 1){ 
            $i++; ?>

        <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-4 d-flex">
            <div class="w-100"
                style="background: #ffffff; border-radius: 24px; padding: 30px 20px; text-align: center; display: flex; flex-direction: column; justify-content: space-between;height: 400px;">

                <div
                    style="border-radius: 50%;  margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;">
                    <span style="width:100%;height: 150px;">
                        <?php $this->load->view('svg_icons/new_application.svg'); ?>
                    </span>
                </div>

                <p style="font-size: 20px; font-weight: 500; color: #111111; margin-bottom: 10px; letter-spacing: 0.5px; line-height: 1.5;">
                    <?= $name['form_name'] . ' ' . $name['form_year'] ?>
        </p>

                <p style="font-size: 14px; color: #8b8b8b; margin-bottom: 20px;padding:0 20px">
                    For new students applying to the school in <?= $name['form_year'] ?>
                </p>

                <div style="margin-top: auto;padding: 15px 10.5px 20px 10.5px;">
                    <form enctype="multipart/form-data" id="instruction-form" class="form-horizontal" method="post">
                        <input type="hidden" name="admission_setting_id" id="admission_setting_id"
                            value="<?= $name['id'] ?>">
                        <input type="hidden" name="auId" id="auId" value="<?= $au_id ?>">
                        <button type="button"
                            onclick="instructionpage('<?= $name['id'] ?>', '<?= $name['enable_partial_payment'] ?>')"
                            style="width: 100%; background:<?php echo !empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] :  '#623CE7' ?>; color: <?php echo !empty($admission_ui_colors['primary_font_color']) ? $admission_ui_colors['primary_font_color'] :  'white' ?>; border: none; padding: 13px 20px; border-radius: 8px; font-weight: 400; font-size: 16px; cursor: pointer;">
                            Start Application
                        </button>
                    </form>
                </div>

            </div>
        </div>

        <?php } ?>
        <?php } ?>

        <?php if ($i == 0) { ?>
        <h3>
            <?= $this->settings->getSetting('admission_applications_closed_display_text') 
                ? $this->settings->getSetting('admission_applications_closed_display_text') 
                : 'Admission form issue closed for maintenance. We will be back soon.'; 
            ?>
        </h3>
        <?php } ?>

    </div>
</div>
<br>
<br>
<?php } ?>

<div class="modal" id="rejected_documents_modal" tabindex="-1" role="dialog" style="margin:auto;" data-backdrop="static"
    aria-labelledby="instruction-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;width:70%">

        <div class="modal-header" style="border-bottom: 2px solid #ccc;">
            <h4 class="modal-title" id="rejected_documents_modalHeader">Re Upload Rejected Documents</h4>
        </div>

        <div class="modal-body">
            <div id="rejected_documents">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Name</th>
                            <th>Uploaded Document</th>
                            <th>Re Upload Document</th>
                            <th>Rejected Remarks</th>
                        </tr>
                    </thead>
                    <tbody id="rejected_doc">

                    </tbody>
                </table>
            </div>
        </div>

        <div class="modal-footer">
            <button class="btn btn-danger" type="button" id="subBtn" data-dismiss="modal">Close </button>
        </div>
    </div>
</div>

<div id="upload_document_model" class="modal" role="dialog" style="margin:auto;" data-backdrop="static"
    aria-hidden="true" tabindex="-1">
    <div class="modal-content modal-dialog" style="border-radius: 8px;width:60%">

        <div class="modal-header" style="border-bottom: 2px solid #ccc;">
            <h5 class="modal-title" id="upload_document_header">Upload Details</h5>
        </div>

        <form action="" id="upload_document_form" enctype="multipart/form-data" method="post" data-parsley-validate=""
            class="form-horizontal">
            <div class="modal-body">
                <input type="hidden" value="" name="document_name" id="document_name">
                <input type="hidden" id="document_no" name="document_no">
                <input type="hidden" id="af_id" name="af_id">
                <input type="hidden" id="adm_setting_id" name="adm_setting_id">
                <div id="upload_aadhar_details" style="margin-left: 15px;"></div>
            </div>
            <div class="modal-footer">
                <input type="button" id="upload_btn" style="width: 9rem; border-radius: .45rem;" class="btn btn-primary"
                    value="Upload" onclick="upload_admission_documents()">
                <button type="button" class="btn btn-danger" style="width: 9rem; border-radius: .45rem;"
                    data-dismiss="modal">Close</button>
            </div>
        </form>
    </div>
</div>

<script type="text/javascript">
function preview_minimum_fields(af_id, au_id, adm_setting_id) {
    let form = $('#form_id_' + af_id); // Use the specific form ID passed as an argument
    form.find('#lastId').val(af_id);
    form.find('#au_id').val(au_id);
    form.find('#admission_setting_id').val(adm_setting_id);
    let url = '<?php echo site_url('admissions/short_application') ?>';
    form.attr('action', url);
    form.submit();
}

function get_rejected_documents(af_id, adm_setting_id) {
    $.ajax({
        url: '<?php echo site_url('admission_controller/get_rejected_documents'); ?>',
        type: 'post',
        data: {
            'af_id': af_id,
            'adm_setting_id': adm_setting_id
        },
        success: function(data) {
            var res = $.parseJSON(data);
            if (res != '') {
                $('#rejected_doc').html(construct_rejected_documents(res, adm_setting_id, af_id));
            } else {
                $('#rejected_doc').html('<h3 class="no-data-display">No Documents Rejected</h3>');
            }
        }
    });
}

function construct_rejected_documents(res, adm_setting_id, af_id) {
    html = '';
    for (var i = 0; i < res.length; i++) {
        html += `<tr>`;
        html += `<td>${i+1}</td>`;
        html += '<td>' + res[i]['document_type'] + '</td>';
        html += '<td><a target="_blank" href="' + res[i]['document_uri'] + '">View</a></td>';
        if ($.type(res[i].view_type) !== 'undefined') {
            html +=
                `<td style="border-right: none;" > <button class="btn btn-info" type="button" data-toggle="modal" data-target="#upload_document_model" id="doc-upload_btn${res[i]['id']}" onclick="show_upload_document_modal('${af_id}','${res[i].view_type}','${res[i]['document_type']}','${res[i]['id']}','${adm_setting_id}')">Upload Details</button></td>`;
        } else {
            html +=
                `<td style="border-right: none;" > <button class="btn btn-info" type="button" data-toggle="modal" data-target="#upload_document_model" id="doc-upload_btn${res[i]['id']}" onclick="show_upload_document_modal('${af_id}','','${res[i]['document_type']}','${res[i]['id']}','${adm_setting_id}')">Upload Details</button></td>`;
            // html += `<td><input type="file" class="form-control" id="doc-upload${res[i]['id']}" onchange="reupload_document_file_path(${res[i]['id']})">
            // 	 <div id="afterSuccessUploadShow_${res[i]['id']}">
            // 	 <span id="percentage_doc_completed${res[i]['id']}" style="font-size: 20px; display: none;">0 %</span>
            //     </div></td>`;
        }
        html += '<td>' + res[i]['verification_remarks'] + '</td>';
        html += `</tr>`;
    }
    return html;
}

function show_upload_document_modal(af_id, view_type, document_name, document_no, adm_setting_id) {
    $('#document_no').val(document_no);
    $('#upload_document_header').html(`Re Upload ${document_name} Details`);
    $('#document_name').val(document_name);
    $('#adm_setting_id').val(adm_setting_id);
    $('#af_id').val(af_id);
    if (view_type == 'aadhar') {
        $('#upload_btn').show();
        $('#upload_aadhar_details').html(construct_aadhar_details(adm_setting_id, document_no));
        $('#upload_btn').show();
    } else if (view_type == 'pan') {
        $('#upload_aadhar_details').html(construct_pan_details(adm_setting_id, document_no));
    } else {
        $('#upload_btn').hide();
        $('#upload_aadhar_details').html(construct_upload_doc__details(document_no, af_id, adm_setting_id));
    }
}

function construct_upload_doc__details(document_no, af_id, adm_setting_id) {
    var html = '';
    html += `<div class="form-group" style=" display: flex; justify-content: left;">
                    <label class="col-md-4  control-label">Upload file</label>
                    <div class="col-md-6 ">
                        <input type="file" class="form-control" id="doc-upload${document_no}" onchange="reupload_document_file_path(${document_no},'${af_id}','${adm_setting_id}')">
                        <span id="percentage_doc_completed${document_no}" style="font-size: 15px; display: none;">0 %</span>
                        <div id="afterSuccessUploadShow_${document_no}">
                        </div>
                    </div>
                </div>`;
    return html;
}

function construct_aadhar_details(adm_setting_id, document_no) {
    var html = '';
    var template = '<?php echo site_url('admission_controller/download_declaration/') ?>' + adm_setting_id + '/' +
        'Aadhar Card Declaration/' + 'aadhar';
    html += `<div class="row" id="has_document" style="margin:10px 0px">
                    <label style="padding-left: 15px;" class="col-md-4" id="">Do you have Aadhar Card?</label>
                    <div class="col-md-8"> 
                        <label class="radio-inline p-0 " for="btn-1" style="padding-top:0px">
                            <input  type="radio" data-parsley-group="block1" name="has_document" id="btn-1" value="1"  onclick="show_aadhar_details(1)">
                            Yes
                        </label>
                        <label class="radio-inline p-0" for="btn-0" style="padding-top:0px">
                            <input type="radio" data-parsley-group="block1" name="has_document" id="btn-0" value="0" onclick="show_aadhar_details(0)">
                            No
                        </label>
                    </div>
                </div>
                <div class="row" id="show_aadhar_data" style="margin-left: 15px 0;display:none">
                    <div class="col-md-12 p-0" style="padding-left:5px">
                        <label for="name_in_aadhar" class="col-md-4">Name As per Aadhar <font color="red">*</font></label>
                        <div class="col-md-6">
                            <div class="input-group">
                            <span class="input-group-addon">
                                <span class="fa fa-pencil"></span>
                            </span>
                            <input class="form-control remove-required" id="name_in_aadhar" placeholder="Enter the name as per Aadhar" name="name_as_per__aadhar" type="text" data-parsley-error-message="Should contain only alphabets or spaces" data-parsley-pattern="^[a-zA-Z. ]+$">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12 mb-3" style="margin-top: 15px;padding-left:5px">
                        <label for="aadhar_number" class="col-md-4">Aadhar Number <font color="red">*</font></label>
                        <div class="col-md-6" >
                            <div class="input-group">
                            <span class="input-group-addon">
                                <span class="fa fa-pencil"></span>
                            </span>
                            <input class="form-control remove-required" id="aadhar_number" placeholder="Enter the Aadhar Number" name="aadhar_number" type="number" data-parsley-minlength="12" data-parsley-maxlength="12" data-parsley-type="number" data-parsley-error-message="Should contain 12 Numbers">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12 p-0" style="margin-top: 15px;padding-left:5px">
                        <label for="" class="col-md-4">Upload Aadhar Document <font color="red">*</font></label>
                        <div class="col-md-6">
                            <input class="form-control remove-required" id="aadhar_doc_file" name="document_file" type="file" accept="application/pdf, image/png, image/jpeg">
                        </div>
                    </div>
                </div>
                <div class="row" id="no_aadhar_document" style="display:none">
                    <label style="padding-left: 15px;" class="col-md-4" id="attach_label">Have you applied for Aadhar Card?</label>
                    <div class="col-md-8"> 
                        <label class="radio-inline p-0" for="btn-2" style="padding-top:0px">
                            <input  type="radio" data-parsley-group="block2" name="applied_form_document" id="btn-2" value="1"  onclick="upload_acknowledgement_details(1)">
                            Yes
                        </label>
                        <label class="radio-inline p-0" for="btn-3" style="padding-top:0px">
                            <input type="radio" data-parsley-group="block2" name="applied_form_document" id="btn-3" value="0" onclick="upload_acknowledgement_details(0)">
                            No
                        </label>
                    </div>
                </div>
                <div class="row" id="show_acknowledgement_data" style="display:none">
                    <div class="col-md-12 p-0" style="margin-top: 15px;padding-left:5px">
                        <label for="" class="col-md-4">Upload Acknowledgement <font color="red">*</font></label>
                        <div class="col-md-6">
                            <input class="form-control remove-required" id="acknowledgement_file" name="acknowledgement_file" type="file" accept="application/pdf, image/png, image/jpeg">
                        </div>
                    </div>
                </div>
                <div class="row" id="download_aadhar_acknowledgement" style="display:none">
                    <div class="col-md-12" style="margin-top: 15px;padding-left:5px">
                        <label for="" class="col-md-4">Download Declaration</label>
                        <div class="col-md-6">
                            <a href="${template}" class="btn btn-info">Download <i class="fa fa-download"></i></a>
                            <span class="help-block">Download the declaration, sign and upload it</span>
                        </div>
                    </div>
                    <div class="col-md-12" style="margin-top: 15px;padding-left:5px">
                        <label for="" class="col-md-4">Upload Declaration <font color="red">*</font></label>
                        <div class="col-md-6">
                            <input class="form-control remove-required" id="aadhar_declaration" name="declaration_file" type="file" accept="application/pdf, image/png, image/jpeg">
                        </div>
                    </div>
                </div>`;
    return html;
}

function construct_pan_details(adm_setting_id, document_no) {
    var html = '';
    var template = '<?php echo site_url('admission_controller/download_declaration/') ?>' + adm_setting_id + '/' +
        'PAN Card Declaration/' + 'pan';
    html += `<div class="row" id="has_pan_document" style="margin:10px 0px">
                    <label style="padding-left: 15px;" class="col-md-4" id="">Do you have PAN Card?</label>
                    <div class="col-md-8"> 
                        <label class="radio-inline p-0" for="panbtn-1" style="padding-top:0px">
                            <input type="radio" data-parsley-group="block3" name="has_document" id="panbtn-1" value="1"  onclick="show_pan_details(1)">
                            Yes
                        </label>
                        <label class="radio-inline p-0" for="panbtn-0" style="padding-top:0px">
                            <input type="radio" data-parsley-group="block3" name="has_document" id="panbtn-0" value="0" onclick="show_pan_details(0)">
                            No
                        </label>
                    </div>
                </div>
                <div class="row" id="show_pancard_data" style="margin-left: 15px 0;display:none">
                    <div class="col-md-12 p-0" style="padding-left:5px">
                        <label for="pan_number" class="col-md-4">PAN Card Number <font color="red">*</font></label>
                        <div class="col-md-6" >
                            <div class="input-group">
                            <span class="input-group-addon">
                                <span class="fa fa-pencil"></span>
                            </span>
                            <input class="form-control remove-required" id="pan_card_number" placeholder="Enter the PAN Card Number" name="pan_card_number"  data-parsley-minlength="10" data-parsley-maxlength="10" pattern="[A-Z0-9]" data-parsley-error-message="Should contain 10 Numbers">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12 p-0" style="margin-top: 15px;padding-left:5px">
                        <label for="" class="col-md-4">Upload PAN Card Document <font color="red">*</font></label>
                        <div class="col-md-6">
                            <input class="form-control remove-required" id="pancard_doc_file" name="document_file" type="file" accept="application/pdf, image/png, image/jpeg">
                        </div>
                    </div>
                </div>
                <div class="row" id="no_pan_document" style="margin-top:15px;display:none">
                    <label style="padding-left: 15px;" class="col-md-4" id="">Have you applied for PAN Card?</label>
                    <div class="col-md-8"> 
                        <label class="radio-inline p-0" for="pan_btn-2" style="padding-top:0px">
                            <input  type="radio" data-parsley-group="block2" name="applied_form_document" id="pan_btn-2" value="1"  onclick="upload_pan_acknowledgement_details(1)">
                            Yes
                        </label>
                        <label class="radio-inline p-0" for="pan_btn-3" style="padding-top:0px">
                            <input type="radio" data-parsley-group="block2" name="applied_form_document" id="pan_btn-3" value="0" onclick="upload_pan_acknowledgement_details(0)">
                            No
                        </label>
                    </div>
                </div>
                <div class="row" id="show_pan_acknowledgement_data" style="display:none">
                    <div class="col-md-12 p-0" style="margin-top: 15px;padding-left:5px">
                        <label for="" class="col-md-4">Upload Acknowledgement <font color="red">*</font></label>
                        <div class="col-md-6">
                            <input class="form-control remove-required" id="pan_acknowledgement" name="acknowledgement_file" type="file" accept="application/pdf, image/png, image/jpeg">
                        </div>
                    </div>
                </div>
                <div class="row" id="download_pancard_acknowledgement" style="display:none">
                    <div class="col-md-12 p-0" style="margin-top: 15px;padding-left:5px">
                        <label for="" class="col-md-4">Download Declaration</label>
                        <div class="col-md-6">
                            <a href="${template}" class="btn btn-info">Download <i class="fa fa-download"></i></a>
                            <span class="help-block">Download the declaration, sign and upload it</span>
                        </div>
                    </div>
                    <div class="col-md-12 p-0" style="margin-top: 15px;padding-left:5px">
                        <label for="" class="col-md-4">Upload Declaration <font color="red">*</font></label>
                        <div class="col-md-6">
                            <input class="form-control remove-required" id="pan_declaration" name="declaration_file" type="file" accept="application/pdf, image/png, image/jpeg">
                        </div>
                    </div>
                </div>`;
    return html;
}

function show_aadhar_details(e) {
    if (e == 1) {
        $('#show_aadhar_data').show();
        $('#no_aadhar_document').hide();
        $('#show_acknowledgement_data').hide();
        $('#download_aadhar_acknowledgement').hide();
        $('#name_in_aadhar').attr('required', 'required');
        $('#aadhar_number').attr('required', 'required');
        $('#aadhar_doc_file').attr('required', 'required');
        $('#acknowledgement_file').removeAttr('required');
        $('#aadhar_declaration').removeAttr('required');
    } else {
        $('#no_aadhar_document').show();
        $('#show_aadhar_data').hide();
        $('#show_acknowledgement_data').hide();
        $('#download_aadhar_acknowledgement').hide();
        $('#name_in_aadhar').removeAttr('required');
        $('#aadhar_number').removeAttr('required');
        $('#aadhar_doc_file').removeAttr('required');
    }
}

function upload_acknowledgement_details(e) {
    if (e == 1) {
        $('#show_acknowledgement_data').show();
        $('#download_aadhar_acknowledgement').hide();
        $('#show_aadhar_data').hide();
        $('#acknowledgement_file').attr('required', 'required');
        $('#aadhar_declaration').removeAttr('required');
    } else {
        $('#download_aadhar_acknowledgement').show();
        $('#show_acknowledgement_data').hide();
        $('#show_aadhar_data').hide();
        $('#acknowledgement_file').removeAttr('required');
        $('#aadhar_declaration').attr('required', 'required');
    }
}

function show_pan_details(e) {
    if (e == 1) {
        $('#show_pancard_data').show();
        $('#no_pan_document').hide();
        $('#show_pan_acknowledgement_data').hide();
        $('#download_pancard_acknowledgement').hide();
        $('#pan_card_number').attr('required', 'required');
        $('#pancard_doc_file').attr('required', 'required');
        $('#pan_acknowledgement').removeAttr('required');
        $('#pan_declaration').removeAttr('required');
    } else {
        $('#no_pan_document').show();
        $('#show_pancard_data').hide();
        $('#show_pan_acknowledgement_data').hide();
        $('#download_pancard_acknowledgement').hide();
        $('#pan_card_number').removeAttr('required');
        $('#pancard_doc_file').removeAttr('required');
    }
}

function upload_pan_acknowledgement_details(e) {
    if (e == 1) {
        $('#show_pan_acknowledgement_data').show();
        $('#download_pancard_acknowledgement').hide();
        $('#show_pancard_data').hide();
        $('#pan_acknowledgement').attr('required', 'required');
        $('#pan_declaration').removeAttr('required');
    } else {
        $('#download_pancard_acknowledgement').show();
        $('#show_pan_acknowledgement_data').hide();
        $('#show_pancard_data').hide();
        $('#pan_declaration').attr('required', 'required');
        $('#pan_acknowledgement').removeAttr('required');
    }
}

function upload_admission_documents() {
    var has_document = $('input[type=radio][name=has_document]:checked').val();
    var applied_for_document = $('input[type=radio][name=applied_form_document]:checked').val();
    if (has_document == undefined) {
        alert('Upload the Document');
        return false;
    } else if (has_document == 0 && applied_for_document == undefined) {
        alert('Upload the Document');
        return false;
    }
    var document_no = $('#document_no').val();
    var $form = $('#upload_document_form');
    if ($form.parsley().validate()) {
        $('#upload_btn').val('Please wait ...').attr('disabled', 'disabled');
        var form = $('#upload_document_form')[0];
        var formData = new FormData(form);
        $.ajax({
            url: '<?php echo site_url('admission_controller/reupload_admission_documents'); ?>',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            success: function(data) {
                parsed_data = $.parseJSON(data);
                if (parsed_data) {
                    $("#upload_btn").prop('disabled', false);
                    $("#upload_btn").html('Submit');
                    $('#upload_document_model').modal('hide');
                    // $('#doc-upload'+document_no).attr('disabled','disabled');
                    $('#doc-upload_btn' + document_no).attr('disabled', 'disabled');
                    var html = '';
                    html += '<a style="margin-top: 1rem;" id="successmessageId' + document_no +
                        '" class="btn btn-success btn-sm"> Uploaded <i class="fa fa-check-circle"></i></a>'
                    html += '<a style="margin-top: 1rem;" onclick="deletedocument_row_new(' + parsed_data +
                        ',' + document_no + ')" id="removeButtonId' + document_no +
                        '" class="remove btn btn-danger  btn-sm"><i class="fa fa-trash-o"></i></a>';
                    $('#afterSuccessUploadShow' + document_no).html(html);
                }
            },
            complete: function() {
                // window.location.reload();
                $('#upload_btn').val('Submit').removeAttr('disabled');
            }
        });
    }
}

function reupload_document_file_path(doc_id, af_id, adm_setting_id) {
    var file = event.target.files[0];
    completed_promises = 0;
    current_percentage = 0;
    total_promises = 1;
    in_progress_promises = total_promises;
    saveFileToStorage_document(file, doc_id, af_id, adm_setting_id);
}

function saveFileToStorage_document(file, doc_id, af_id, adm_setting_id) {
    $('#percentage_doc_completed' + doc_id).show();
    $('#doc-upload' + doc_id).attr('disabled', 'disabled');
    $.ajax({
        url: '<?php echo site_url("S3_admission_controller/getSignedUrl"); ?>',
        type: 'post',
        data: {
            'filename': file.name,
            'file_type': file.type,
            'folder': 'profile'
        },
        success: function(response) {
            single_file_progress_doc(0);
            response = JSON.parse(response);
            var path = response.path;
            var signedUrl = response.signedUrl;
            $.ajax({
                url: signedUrl,
                type: 'PUT',
                headers: {
                    "Content-Type": file.type,
                    "x-amz-acl": "public-read"
                },
                processData: false,
                data: file,
                xhr: function() {
                    var xhr = $.ajaxSettings.xhr();
                    xhr.upload.onprogress = function(e) {
                        // For uploads
                        if (e.lengthComputable) {
                            single_file_progress_doc(e.loaded / e.total * 100 | 0, doc_id);
                        }
                    };
                    return xhr;
                },
                success: function(response) {
                    // return false;
                    $('#percentage_doc_completed' + doc_id).hide();
                    update_admission_student_documents(path, doc_id, af_id, adm_setting_id);
                    $('#doc-upload' + doc_id).removeAttr('disabled');
                    // resolve({path:path, name:file.name, type:file.type});
                    // increaseLoading();
                },
                error: function(err) {
                    // console.log(err);
                    reject(err);
                }
            });
        },
        error: function(err) {
            reject(err);
        }
    });

}

function single_file_progress_doc(percentage, doc_id) {
    if (percentage == 100) {
        in_progress_promises--;
        if (in_progress_promises == 0) {
            current_percentage = percentage;
        }
    } else {
        if (current_percentage < percentage) {
            current_percentage = percentage;
        }
    }
    $("#percentage_doc_completed" + doc_id).html(`${current_percentage} %`);
    return false;
}

function update_admission_student_documents(path, doc_id, af_id, adm_setting_id) {
    // console.log(document_for);
    $.ajax({
        url: '<?php echo site_url('admission_controller/reupload_rejected_documents'); ?>',
        type: 'post',
        data: {
            'path': path,
            'doc_id': doc_id,
            'af_id': af_id,
            'adm_setting_id': adm_setting_id
        },
        success: function(data) {
            var docrowId = data.trim();
            if (docrowId != 0) {
                $('#doc-upload' + doc_id).attr('disabled', 'disabled');
                $('#doc-upload_btn' + doc_id).attr('disabled', 'disabled');
                var html = '';
                html += '<a style="margin-top: 1rem;" id="successmessageId' + doc_id +
                    '" class="btn btn-success btn-sm"> Uploaded <i class="fa fa-check-circle"></i></a>'
                // html += '<a style="margin-top: 1rem;" onclick="deletedocument_row_new('+doc_id+')" id="removeButtonId'+doc_id+'" class="remove btn btn-danger  btn-sm"><i class="fa fa-trash-o"></i></a>';
                $('#afterSuccessUploadShow_' + doc_id).html(html);
                $('#doc-upload_btn' + doc_id).html('Uploaded');
                // $("#remove"+doc_id).hide();
            }
        }
    });
}

function deletedocument_row_new(doc_id) {
    $.ajax({
        url: '<?php echo site_url('admission_controller/delete_documentbyId'); ?>',
        type: 'post',
        data: {
            'd_id': doc_id
        },
        success: function(data) {
            if (data != '') {
                $('#removeButtonId' + doc_id).hide();
                $('#successmessageId' + doc_id).hide();
                $('#doc-upload' + doc_id).removeAttr('disabled');
                $('#doc-upload' + doc_id).val('');
                // $("#remove"+sl).show();
            }
        }
    });
}

function instructionpage(id, enable_partial_payment) {
    var au_id = $('#auId').val();
    $('#admission_setting_id').val(id);
    // $('#instruction-form').submit();
    $.ajax({
        url: '<?php echo site_url('admission_controller/check_admissions_draft')?>',
        type: 'post',
        data: {
            'au_id': au_id
        },
        success: function(data) {
            var Data = $.parseJSON(data);
            if (data == 0) {
                if (enable_partial_payment == 1) {
                    url = '<?php echo site_url('admissions/start_application') ?>';
                } else {
                    url = '<?php echo site_url('admission_controller/created_application_no') ?>';
                }
                $('#instruction-form').attr('action', url);
                $('#instruction-form').submit();
            } else {
                // $("body").addClass("modal1");
                // $(".modal1").css("display", 'contents');

                Swal.fire({
                title: 'You Have Draft Applications',
                html: `
                    <p style="font-size:15px; color:#6B7280; margin-top:8px;">
                        It looks like you already have one or more applications saved as drafts.
                    </p>`,
                showConfirmButton: false,
                showCancelButton: false,
                width: '400px',
                customClass: {
                    popup: 'rounded-xl shadow-lg',
                    title: 'text-lg font-semibold text-gray-800',
                    htmlContainer: 'text-sm text-gray-500'
                },
                didRender: () => {
                    const popup = Swal.getPopup();

                    // 🔼 Add padding above the title
                    const topPadding = document.createElement('div');
                    topPadding.style.height = '20px';
                    popup.insertBefore(topPadding, popup.firstChild);

                    // ✅ Footer with buttons
                    const container = Swal.getHtmlContainer();
                    const footer = document.createElement('div');
                    footer.className = 'custom-button-container mt-3 d-flex justify-content-center gap-3';
                    footer.innerHTML = `
                        <button class="swal2-cancel btn-outline" id="cancelBtn">Cancel</button>
                        <button class="swal2-confirm btn-primary" id="createBtn">Create New</button>
                    `;
                    container.parentNode.appendChild(footer);

                    // 🔽 Add padding below the buttons
                    const bottomPadding = document.createElement('div');
                    bottomPadding.style.height = '20px';
                    footer.parentNode.appendChild(bottomPadding);

                    // Button actions
                    document.getElementById('cancelBtn').addEventListener('click', () =>
                        Swal.close());

                    document.getElementById('createBtn').addEventListener('click', () => {
                        let url = (enable_partial_payment == 1)
                            ? '<?php echo site_url('admissions/start_application') ?>'
                            : '<?php echo site_url('admission_controller/created_application_no') ?>';

                        $('#instruction-form').attr('action', url).submit();
                    });
                }
            });

            }
        }
    });
}
</script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<style type="text/css">



.container {
    width: 100%;
    margin: auto;
}

@media (min-width: 1200px) {
    .card {
        min-height: 350px;
        /* Makes cards bigger */
        max-width: 400px;
        /* Increases max width */
        font-size: 18px;
    }
}

.icon {
    font-size: 40px;
    margin-bottom: 10px;
}

.custom-button-container {
    display: flex;
    justify-content: center;
    /* Center the buttons horizontally */
    gap: 12px;
    /* Minimal horizontal space between buttons */
    margin-top: 10px;
    /* Slight vertical gap from message */
}

.swal2-confirm.btn-primary {
    background-color: <?php echo ! empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] :  '#623CE7' ?>;
    color: <?php echo !empty($admission_ui_colors['primary_font_color']) ? $admission_ui_colors['primary_font_color'] :  'white' ?>;
    border: none;
    padding: 10px 18px;
    font-size: 14px;
    border-radius: 8px;
    font-weight: 400;
    min-width: 120px;
}

.swal2-cancel.btn-outline {
    background: transparent;
    color:<?php echo ! empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] :  '#623CE7' ?>;
    border: 2px solid <?php echo ! empty($admission_ui_colors['primary_background_color']) ? $admission_ui_colors['primary_background_color'] :  '#623CE7' ?>;
    padding: 10px 18px;
    font-size: 14px;
    border-radius: 8px;
    font-weight: 400;
    min-width: 120px;
}

.rounded-xl {
    border-radius: 12px;
    /* You can adjust this value */
}

<?php if(!$this->mobile_detect->isTablet() && $this->mobile_detect->isMobile()){ ?>
    .admission-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 24px;
}
<?php } ?>

body{
    background-color: #F9F7FE;
}

</style>