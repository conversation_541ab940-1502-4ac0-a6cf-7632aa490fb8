<?php 
$admission_ui_colors = [];
$ui_colors_array = $this->settings->getSetting('admissions_ui_theme_color');

if (!empty($ui_colors_array)) {
    $admission_ui_colors = array_column($ui_colors_array, 'value', 'name');
} ?>
            <div style="background-color: #CBBCFF; height: 38px; display: flex; align-items: center; justify-content: center;">
                <span style="color:black;font-size:14px">Powered by <strong><?php echo $this->settings->getSetting('company_name'); ?></strong></span>
            </div>
        </div>

    <style type="text/css">
        html, body {
            height: 100%;
            margin: 0;
            display: flex;
            flex-direction: column;
        }

.main-content {
    flex: 1;
}
        .message-box .mb-container{
            background: #fff;
        }
        .x-navigation li.active > a{
            background: #00701a;
        }

        @media (max-width: 768px) {
            .message-box .mb-container .mb-middle{
                width: 100%;
                padding: 0;
                margin-left: -25%;
            }
        }

    </style>
        <!-- <PERSON><PERSON> MESSAGE BOX-->

        <!-- START PRELOADS -->
        <!-- <audio id="audio-alert" src="<?php echo base_url();?>audio/alert.mp3" preload="auto"></audio>
        <audio id="audio-fail" src="<?php echo base_url();?>audio/fail.mp3" preload="auto"></audio> -->
        <!-- END PRELOADS -->                  
        
        <!-- START SCRIPTS -->

         <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/jquery/jquery.min.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/jquery/jquery-ui.min.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/bootstrap/bootstrap.min.js"></script>        

        <!-- START THIS PAGE PLUGINS-->        
        <script type='text/javascript' src='<?php echo base_url();?>assets/js/plugins/icheck/icheck.min.js'></script>        
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/mcustomscrollbar/jquery.mCustomScrollbar.min.js"></script>
       
        <script type='text/javascript' src='<?php echo base_url();?>assets/js/plugins/bootstrap/bootstrap-datepicker.js'></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/owl/owl.carousel.min.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/moment.min.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/bootstrap/bootstrap-select.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/bootstrap/bootbox.min.js"></script>
        <!-- END THIS PAGE PLUGINS-->
        
        <!--  For Datatables -->
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/datatables/jquery.dataTables.min.js"></script>
        
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/bootstrap/bootstrap-timepicker.min.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/actions.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/parsley.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/bootstrap-datetimepicker.js"></script>
        <script type="text/javascript" src="<?php echo base_url('assets/js/common/multiselect.min.js');?>"></script>
        <!-- <script type='text/javascript' src='<?php echo base_url();?>assets/js/chung-timepicker.js'></script>  -->
        <script type='text/javascript' src='<?php echo base_url();?>assets/js/pnotify.js'></script>
        <script type='text/javascript' src='<?php echo base_url();?>assets/js/pnotify.buttons.js'></script> 
        <!--For Calendar-->
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/monthly.js"></script>
        <!-- End For Calendar-->
        
        <script type="text/javascript">
        $('#demo-form').parsley();
        </script>
        <script type="text/javascript">
            var $form = $('#demo-form');
            $('.submitFormSingleClick').click (function () {
                if ($form.parsley().validate()){
                  //console.log ( 'valid' );
                  $(this).val('Please wait ...').attr('disabled','disabled');
                  $('#demo-form').submit(); 
                }
            });
        </script>
    </body>
</html>